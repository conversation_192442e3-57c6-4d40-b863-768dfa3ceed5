# PaySMS App

A modern, responsive payment management application built with Vue 3, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Multi-language Support**: Chinese (Simplified), English, Hindi, Portuguese (Brazil)
- **Responsive Design**: Mobile-first design that works on all devices
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Type Safety**: Full TypeScript support for better development experience
- **State Management**: Pinia for reactive state management
- **API Integration**: Complete API integration with mock data for development

### Core Functionality

- **Balance Management**: View total balance, withdrawable amount, and cash bonus
- **Deposit System**: Multiple payment methods with real-time fee calculation
- **Withdrawal System**: Bank account management and instant withdrawals
- **Transaction History**: Complete transaction tracking with status updates
- **Payment Methods**: Bank accounts and UPI wallet management
- **Account Verification**: KYC status and verification flow

## 🛠️ Tech Stack

- **Frontend Framework**: Vue 3 with Composition API
- **Type System**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix <PERSON> + Custom Components
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Internationalization**: Vue I18n
- **HTTP Client**: Axios
- **Icons**: Lucide Vue Next
- **Build Tool**: Vite
- **Package Manager**: Bun

## 📦 Installation

1. **Install dependencies**

   ```bash
   bun install
   ```

2. **Start development server**

   ```bash
   bun run dev
   ```

3. **Build for production**
   ```bash
   bun run build
   ```

## 🌐 API Integration

The application supports both real API integration and mock data for development:

- **Mock API**: Used in development mode for testing
- **Real API**: Production-ready API integration
- **Type Safety**: Full TypeScript types for all API responses

## 🌍 Internationalization

Supports 4 languages with complete translations:

- 🇨🇳 Chinese (Simplified) - Default
- 🇺🇸 English
- 🇮🇳 Hindi
- 🇧🇷 Portuguese (Brazil)

## 📱 Pages

1. **Home** (`/`) - Balance overview and quick actions
2. **Deposit** (`/pages/deposit/add/add`) - Add money to account
3. **Withdraw** (`/pages/withdraw/index`) - Withdraw money to bank
4. **Transactions** (`/pages/transaction/transaction`) - Transaction history
5. **Manage Payments** (`/pages/deposit/managepayment/managepayment`) - Payment methods
6. **Add Bank Account** (`/pages/deposit/addcreditcard/addcreditcard`) - Add new bank account

## 🧪 Testing

Access the test page at `/test` to verify all API integrations and functionality.

## 🚀 Deployment

1. **Build the application**

   ```bash
   bun run build
   ```

2. **Preview the build**

   ```bash
   bun run preview
   ```

3. **Deploy to your hosting platform**
   - The `dist` folder contains all static files
   - Configure your server to serve `index.html` for all routes (SPA mode)

---

Built with ❤️ using Vue 3 and modern web technologies.
