// composables/useNetworkStatus.ts - 网络状态监控
import { ref, onMounted, onUnmounted } from 'vue'

export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref<string>('unknown')
  const effectiveType = ref<string>('unknown')
  const downlink = ref<number>(0)
  const rtt = ref<number>(0)

  // 更新连接信息
  const updateConnectionInfo = () => {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection

    if (connection) {
      connectionType.value = connection.type || 'unknown'
      effectiveType.value = connection.effectiveType || 'unknown'
      downlink.value = connection.downlink || 0
      rtt.value = connection.rtt || 0
    }
  }

  // 网络状态变化处理
  const handleOnline = () => {
    isOnline.value = true
    updateConnectionInfo()

    // 显示网络恢复提示
    showNetworkToast('network.restored', 'success')
  }

  const handleOffline = () => {
    isOnline.value = false

    // 显示网络断开提示
    showNetworkToast('network.disconnected', 'error')
  }

  const handleConnectionChange = () => {
    updateConnectionInfo()

    // 根据网络质量显示提示
    if (effectiveType.value === 'slow-2g' || effectiveType.value === '2g') {
      showNetworkToast('network.slow', 'warning')
    }
  }

  // 显示网络状态提示
  const showNetworkToast = async (messageKey: string, type: 'success' | 'error' | 'warning') => {
    try {
      const { useToast } = await import('@/composables/useToast')
      const toast = useToast()

      // 这里应该使用 i18n，但为了避免循环依赖，暂时使用硬编码
      // 在实际使用时，应该在组件中调用并传入翻译后的文本
      const messages = {
        'network.restored': 'Network connection restored',
        'network.disconnected': 'Network connection lost, please check your network settings',
        'network.slow': 'Network connection is slow, may affect user experience'
      }

      const message = messages[messageKey as keyof typeof messages] || messageKey

      switch (type) {
        case 'success':
          toast.success(message)
          break
        case 'error':
          toast.error(message, undefined, { persistent: true })
          break
        case 'warning':
          toast.warning(message)
          break
      }
    } catch (error) {
      console.warn('Failed to show network toast:', error)
    }
  }

  // 检查网络质量
  const getNetworkQuality = (): 'excellent' | 'good' | 'fair' | 'poor' | 'unknown' => {
    if (!isOnline.value) return 'poor'

    switch (effectiveType.value) {
      case '4g':
        return 'excellent'
      case '3g':
        return 'good'
      case '2g':
        return 'fair'
      case 'slow-2g':
        return 'poor'
      default:
        return 'unknown'
    }
  }

  // 获取网络质量颜色
  const getQualityColor = () => {
    const quality = getNetworkQuality()
    const colors = {
      excellent: 'text-green-600',
      good: 'text-blue-600',
      fair: 'text-yellow-600',
      poor: 'text-red-600',
      unknown: 'text-gray-600'
    }
    return colors[quality]
  }

  // 获取网络质量文本 (应该在组件中使用 i18n)
  const getQualityText = () => {
    const quality = getNetworkQuality()
    const texts = {
      excellent: 'Excellent',
      good: 'Good',
      fair: 'Fair',
      poor: 'Poor',
      unknown: 'Unknown'
    }
    return texts[quality]
  }

  // 测试网络延迟
  const testLatency = async (): Promise<number> => {
    try {
      const start = performance.now()
      await fetch('/api/ping', {
        method: 'HEAD',
        cache: 'no-cache'
      })
      const end = performance.now()
      return Math.round(end - start)
    } catch (error) {
      return -1 // 表示测试失败
    }
  }

  // 重试网络请求
  const retryWithBackoff = async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: Error

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error

        if (i === maxRetries) break

        // 指数退避
        const delay = baseDelay * Math.pow(2, i)
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }

    throw lastError!
  }

  onMounted(() => {
    // 初始化连接信息
    updateConnectionInfo()

    // 添加事件监听器
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 监听连接变化
    const connection = (navigator as any).connection
    if (connection) {
      connection.addEventListener('change', handleConnectionChange)
    }
  })

  onUnmounted(() => {
    // 移除事件监听器
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)

    const connection = (navigator as any).connection
    if (connection) {
      connection.removeEventListener('change', handleConnectionChange)
    }
  })

  return {
    isOnline,
    connectionType,
    effectiveType,
    downlink,
    rtt,
    getNetworkQuality,
    getQualityColor,
    getQualityText,
    testLatency,
    retryWithBackoff,
    updateConnectionInfo
  }
}
