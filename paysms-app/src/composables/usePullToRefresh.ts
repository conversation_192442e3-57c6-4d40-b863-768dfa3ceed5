// composables/usePullToRefresh.ts - 下拉刷新功能
import { ref, onMounted, onUnmounted, type Ref } from 'vue'

interface PullToRefreshOptions {
  threshold?: number      // 触发刷新的距离阈值
  maxDistance?: number    // 最大下拉距离
  resistance?: number     // 阻力系数 (0-1)
  snapBackDuration?: number // 回弹动画时长
  refreshDuration?: number  // 刷新动画最小时长
}

export function usePullToRefresh(
  container: Ref<HTMLElement | null>,
  onRefresh: () => Promise<void>,
  options: PullToRefreshOptions = {}
) {
  const {
    threshold = 80,
    maxDistance = 120,
    resistance = 0.5,
    snapBackDuration = 300,
    refreshDuration = 1000
  } = options

  const isPulling = ref(false)
  const isRefreshing = ref(false)
  const pullDistance = ref(0)
  const canRefresh = ref(false)

  let startY = 0
  let currentY = 0
  let isAtTop = true

  // 检查是否在顶部
  const checkScrollTop = () => {
    if (!container.value) return false
    return container.value.scrollTop <= 0
  }

  // 计算拉动距离（应用阻力）
  const calculatePullDistance = (distance: number): number => {
    if (distance <= 0) return 0
    
    // 应用阻力公式：distance * resistance^(distance/threshold)
    const resistanceFactor = Math.pow(resistance, distance / threshold)
    const calculatedDistance = distance * resistanceFactor
    
    return Math.min(calculatedDistance, maxDistance)
  }

  // 更新拉动状态
  const updatePullState = (distance: number) => {
    pullDistance.value = calculatePullDistance(distance)
    canRefresh.value = pullDistance.value >= threshold
  }

  // 触摸开始
  const handleTouchStart = (e: TouchEvent) => {
    if (isRefreshing.value) return
    
    isAtTop = checkScrollTop()
    if (!isAtTop) return

    startY = e.touches[0].clientY
    currentY = startY
  }

  // 触摸移动
  const handleTouchMove = (e: TouchEvent) => {
    if (isRefreshing.value || !isAtTop) return

    currentY = e.touches[0].clientY
    const deltaY = currentY - startY

    if (deltaY > 0) {
      // 向下拉动
      isPulling.value = true
      updatePullState(deltaY)
      
      // 阻止默认滚动行为
      if (pullDistance.value > 0) {
        e.preventDefault()
      }
    } else {
      // 向上滑动，重置状态
      isPulling.value = false
      pullDistance.value = 0
      canRefresh.value = false
    }
  }

  // 触摸结束
  const handleTouchEnd = async () => {
    if (isRefreshing.value) return

    if (canRefresh.value && isPulling.value) {
      // 触发刷新
      isRefreshing.value = true
      pullDistance.value = threshold // 保持在阈值位置
      
      try {
        // 确保刷新动画至少显示指定时长
        const refreshPromise = onRefresh()
        const minDurationPromise = new Promise(resolve => 
          setTimeout(resolve, refreshDuration)
        )
        
        await Promise.all([refreshPromise, minDurationPromise])
      } catch (error) {
        console.error('Refresh failed:', error)
      } finally {
        // 回弹动画
        await snapBack()
        isRefreshing.value = false
      }
    } else {
      // 回弹到顶部
      await snapBack()
    }

    isPulling.value = false
    canRefresh.value = false
  }

  // 回弹动画
  const snapBack = (): Promise<void> => {
    return new Promise(resolve => {
      const startDistance = pullDistance.value
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / snapBackDuration, 1)
        
        // 使用缓出动画
        const easeOut = 1 - Math.pow(1 - progress, 3)
        pullDistance.value = startDistance * (1 - easeOut)

        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          pullDistance.value = 0
          resolve()
        }
      }

      requestAnimationFrame(animate)
    })
  }

  // 添加事件监听器
  const addEventListeners = () => {
    if (!container.value) return

    const element = container.value
    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })
    element.addEventListener('touchcancel', handleTouchEnd, { passive: true })
  }

  // 移除事件监听器
  const removeEventListeners = () => {
    if (!container.value) return

    const element = container.value
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
    element.removeEventListener('touchcancel', handleTouchEnd)
  }

  onMounted(() => {
    addEventListeners()
  })

  onUnmounted(() => {
    removeEventListeners()
  })

  // 手动触发刷新
  const triggerRefresh = async () => {
    if (isRefreshing.value) return

    isRefreshing.value = true
    pullDistance.value = threshold

    try {
      await onRefresh()
    } finally {
      await snapBack()
      isRefreshing.value = false
    }
  }

  return {
    isPulling,
    isRefreshing,
    pullDistance,
    canRefresh,
    triggerRefresh,
    
    // 用于样式绑定的计算属性
    pullProgress: ref(() => Math.min(pullDistance.value / threshold, 1)),
    refreshIndicatorStyle: ref(() => ({
      transform: `translateY(${pullDistance.value}px)`,
      opacity: isPulling.value || isRefreshing.value ? 1 : 0
    }))
  }
}
