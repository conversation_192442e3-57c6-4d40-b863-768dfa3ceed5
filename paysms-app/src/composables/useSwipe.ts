// composables/useSwipe.ts - 滑动手势管理
import { ref, onMounted, onUnmounted, type Ref } from 'vue'

interface SwipeOptions {
  threshold?: number // 最小滑动距离
  timeout?: number   // 最大滑动时间
  passive?: boolean  // 被动监听
}

interface SwipeState {
  startX: number
  startY: number
  startTime: number
  endX: number
  endY: number
  endTime: number
}

export function useSwipe(
  target: Ref<HTMLElement | null>,
  options: SwipeOptions = {}
) {
  const {
    threshold = 50,
    timeout = 500,
    passive = true
  } = options

  const isSwiping = ref(false)
  const direction = ref<'left' | 'right' | 'up' | 'down' | null>(null)
  
  const swipeState = ref<SwipeState>({
    startX: 0,
    startY: 0,
    startTime: 0,
    endX: 0,
    endY: 0,
    endTime: 0
  })

  // 事件回调
  const onSwipeStart = ref<((e: TouchEvent) => void) | null>(null)
  const onSwipeMove = ref<((e: TouchEvent) => void) | null>(null)
  const onSwipeEnd = ref<((e: TouchEvent) => void) | null>(null)
  const onSwipeLeft = ref<(() => void) | null>(null)
  const onSwipeRight = ref<(() => void) | null>(null)
  const onSwipeUp = ref<(() => void) | null>(null)
  const onSwipeDown = ref<(() => void) | null>(null)

  const handleTouchStart = (e: TouchEvent) => {
    if (!e.touches.length) return
    
    const touch = e.touches[0]
    swipeState.value.startX = touch.clientX
    swipeState.value.startY = touch.clientY
    swipeState.value.startTime = Date.now()
    
    isSwiping.value = true
    direction.value = null
    
    onSwipeStart.value?.(e)
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!isSwiping.value || !e.touches.length) return
    
    const touch = e.touches[0]
    swipeState.value.endX = touch.clientX
    swipeState.value.endY = touch.clientY
    
    onSwipeMove.value?.(e)
  }

  const handleTouchEnd = (e: TouchEvent) => {
    if (!isSwiping.value) return
    
    swipeState.value.endTime = Date.now()
    
    const deltaX = swipeState.value.endX - swipeState.value.startX
    const deltaY = swipeState.value.endY - swipeState.value.startY
    const deltaTime = swipeState.value.endTime - swipeState.value.startTime
    
    // 检查是否满足滑动条件
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    
    if (distance >= threshold && deltaTime <= timeout) {
      // 确定滑动方向
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (deltaX > 0) {
          direction.value = 'right'
          onSwipeRight.value?.()
        } else {
          direction.value = 'left'
          onSwipeLeft.value?.()
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          direction.value = 'down'
          onSwipeDown.value?.()
        } else {
          direction.value = 'up'
          onSwipeUp.value?.()
        }
      }
    }
    
    isSwiping.value = false
    onSwipeEnd.value?.(e)
  }

  const addEventListeners = () => {
    if (!target.value) return
    
    const element = target.value
    const options = { passive }
    
    element.addEventListener('touchstart', handleTouchStart, options)
    element.addEventListener('touchmove', handleTouchMove, options)
    element.addEventListener('touchend', handleTouchEnd, options)
    element.addEventListener('touchcancel', handleTouchEnd, options)
  }

  const removeEventListeners = () => {
    if (!target.value) return
    
    const element = target.value
    
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
    element.removeEventListener('touchcancel', handleTouchEnd)
  }

  onMounted(() => {
    addEventListeners()
  })

  onUnmounted(() => {
    removeEventListeners()
  })

  // 重新绑定事件（当 target 改变时）
  const rebind = () => {
    removeEventListeners()
    addEventListeners()
  }

  return {
    isSwiping,
    direction,
    swipeState,
    
    // 事件设置器
    onSwipeStart: (callback: (e: TouchEvent) => void) => {
      onSwipeStart.value = callback
    },
    onSwipeMove: (callback: (e: TouchEvent) => void) => {
      onSwipeMove.value = callback
    },
    onSwipeEnd: (callback: (e: TouchEvent) => void) => {
      onSwipeEnd.value = callback
    },
    onSwipeLeft: (callback: () => void) => {
      onSwipeLeft.value = callback
    },
    onSwipeRight: (callback: () => void) => {
      onSwipeRight.value = callback
    },
    onSwipeUp: (callback: () => void) => {
      onSwipeUp.value = callback
    },
    onSwipeDown: (callback: () => void) => {
      onSwipeDown.value = callback
    },
    
    rebind
  }
}
