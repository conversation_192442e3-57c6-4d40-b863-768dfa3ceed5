// composables/useHaptics.ts - 触觉反馈管理
import { ref } from 'vue'

interface HapticOptions {
  intensity?: 'light' | 'medium' | 'heavy'
  duration?: number
}

class HapticManager {
  private isSupported = ref(false)

  constructor() {
    this.checkSupport()
  }

  private checkSupport() {
    // 检查是否支持触觉反馈
    this.isSupported.value = 'vibrate' in navigator || 'hapticFeedback' in navigator
  }

  // 轻触反馈 - 用于按钮点击、选择等
  light(options?: HapticOptions) {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(10) // 10ms 轻触
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 中等强度反馈 - 用于重要操作确认
  medium(options?: HapticOptions) {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(25) // 25ms 中等
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 强烈反馈 - 用于错误、警告等
  heavy(options?: HapticOptions) {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(50) // 50ms 强烈
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 成功反馈 - 双击模式
  success() {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate([25, 50, 25]) // 短-停-短
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 错误反馈 - 三击模式
  error() {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate([50, 25, 50, 25, 50]) // 长-短-长-短-长
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 警告反馈 - 渐强模式
  warning() {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate([25, 25, 50]) // 短-停-长
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 选择反馈 - 用于滑动选择、切换等
  selection() {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(5) // 5ms 极轻
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }

  // 自定义模式
  custom(pattern: number | number[]) {
    if (!this.isSupported.value) return

    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(pattern)
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error)
    }
  }
}

const hapticManager = new HapticManager()

export function useHaptics() {
  return {
    isSupported: hapticManager.isSupported,
    light: hapticManager.light.bind(hapticManager),
    medium: hapticManager.medium.bind(hapticManager),
    heavy: hapticManager.heavy.bind(hapticManager),
    success: hapticManager.success.bind(hapticManager),
    error: hapticManager.error.bind(hapticManager),
    warning: hapticManager.warning.bind(hapticManager),
    selection: hapticManager.selection.bind(hapticManager),
    custom: hapticManager.custom.bind(hapticManager)
  }
}

// 全局触觉反馈实例
export const haptics = hapticManager
