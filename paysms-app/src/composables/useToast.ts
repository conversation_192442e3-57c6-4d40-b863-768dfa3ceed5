// composables/useToast.ts - Toast 通知管理
import { ref, createApp, type App } from 'vue'
import Toast from '@/components/common/Toast.vue'

interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  persistent?: boolean
}

interface ToastInstance {
  id: string
  app: App
  container: HTMLElement
  close: () => void
}

class ToastManager {
  private toasts = ref<ToastInstance[]>([])
  private idCounter = 0

  show(options: ToastOptions) {
    const id = `toast-${++this.idCounter}`
    const container = document.createElement('div')
    container.id = id
    
    const app = createApp(Toast, {
      ...options,
      onClose: () => this.remove(id)
    })
    
    const instance = app.mount(container)
    document.body.appendChild(container)
    
    const toastInstance: ToastInstance = {
      id,
      app,
      container,
      close: () => {
        if (instance && typeof instance.close === 'function') {
          instance.close()
        }
      }
    }
    
    this.toasts.value.push(toastInstance)
    
    return toastInstance
  }

  success(message: string, title?: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'success',
      message,
      title,
      ...options
    })
  }

  error(message: string, title?: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'error',
      message,
      title,
      persistent: true, // 错误消息默认持久显示
      ...options
    })
  }

  warning(message: string, title?: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'warning',
      message,
      title,
      ...options
    })
  }

  info(message: string, title?: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'info',
      message,
      title,
      ...options
    })
  }

  private remove(id: string) {
    const index = this.toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      const toast = this.toasts.value[index]
      
      // 延迟移除 DOM 元素，确保动画完成
      setTimeout(() => {
        if (toast.container.parentNode) {
          toast.container.parentNode.removeChild(toast.container)
        }
        toast.app.unmount()
      }, 300)
      
      this.toasts.value.splice(index, 1)
    }
  }

  clear() {
    this.toasts.value.forEach(toast => {
      toast.close()
    })
  }
}

const toastManager = new ToastManager()

export function useToast() {
  return {
    toast: toastManager,
    success: toastManager.success.bind(toastManager),
    error: toastManager.error.bind(toastManager),
    warning: toastManager.warning.bind(toastManager),
    info: toastManager.info.bind(toastManager),
    clear: toastManager.clear.bind(toastManager)
  }
}

// 全局 toast 实例
export const toast = toastManager
