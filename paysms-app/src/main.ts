import { createApp } from 'vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import App from './App.vue'
import './style.css'

// Import routes
import { routes } from './router/index'

// Import locales
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'
import hiIN from './locales/hi-IN'
import ptBR from './locales/pt-BR'

// Create router
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// Create i18n
const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'hi-IN': hiIN,
    'pt-BR': ptBR
  }
})

// Create pinia
const pinia = createPinia()

// Create app
const app = createApp(App)

app.use(router)
app.use(pinia)
app.use(i18n)

app.mount('#app')

// 开发环境下导入翻译检查工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/translationChecker')
}
