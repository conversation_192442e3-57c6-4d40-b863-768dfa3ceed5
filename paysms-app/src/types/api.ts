// types/api.ts - 基于真实API分析的类型定义

// 通用API响应格式
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 用户相关类型
export interface UserInfo {
  uid: number
  coin: number          // 总余额
  dcoin: number         // 可提现余额
  ecoin: number         // 已添加金额(未使用)
  bonus: number         // 现金奖金
  totalcoin: number     // 总余额
  kyc: number           // KYC状态 (0=未验证, 1=已验证)
  svip: number          // VIP状态
  ispayer: number       // 支付者状态
  nodislabelid: number  // 标签ID
}

export interface BalanceResponse {
  user: UserInfo
  channels: PaymentChannel[]
  memo: string          // 提示信息 (HTML格式)
  popmsg: string        // 弹窗消息
  customer: any[]       // 客服信息
  url: string           // 客服URL
}

// 支付相关类型
export interface PaymentChannel {
  id: number
  title: string         // 支付方式名称 (如 "AliPay", "upi")
  icon: string          // 图标URL
  subtype: string       // 子类型 (如 "onlinepay")
  mincoin: number       // 最小金额
  maxcoin: number       // 最大金额
  disrate: string       // 折扣率显示 (如 "+500", "+3.00%")
  discoin: number       // 折扣金额
  type: number          // 类型
  pages: PaymentPage[]  // 支付页面配置
}

export interface PaymentPage {
  id: number
  title: string         // 页面标题 (如 "test1009", "Paytm APP")
  type: number          // 页面类型
  banktype: number      // 银行类型
  mincoin: number       // 最小金额
  maxcoin: number       // 最大金额
  disrate: string       // 折扣率显示
  discoin: number       // 折扣金额
  rate: number          // 费率 (小数形式，如 0.03 表示 3%)
}

export interface BankAccount {
  id: number
  accountNumber: string
  ifscCode: string
  accountHolderName: string
  bankName: string
  isVerified: boolean
}

export interface UpiWallet {
  id: number
  name: string          // 钱包名称 (如 "Paytm", "Phonepe")
  checked: boolean      // 是否已验证
  card: string          // 状态文本 (如 "Link")
  icon: string          // 图标路径
  cat: number           // 分类ID
}

// 交易相关类型
export interface TransactionItem {
  id: number
  orderid: string       // 订单ID
  coin: string          // 金额 (字符串格式，如 "300.00")
  status_str: string    // 状态文本 (如 "In-Process", "Success")
  status: number        // 状态码 (0=处理中, 1=成功, 2=失败, 3=退款)
  time: string          // 时间 (如 "12:48 pm")
  title: string         // 支付方式 (如 "AliPay", "upi")
  memo: string          // 备注
}

export interface TransactionHistory {
  deplist: Record<string, TransactionItem[]>    // 存款记录 (按日期分组)
  drawlist: TransactionItem[]                   // 提现记录
  betlist: TransactionItem[]                    // 投注记录
  bonuslist: TransactionItem[]                  // 奖金记录
  banklist: TransactionItem[]                   // 银行记录
  show: number                                  // 显示标志
  url: string                                   // 客服URL
}

export interface BanksResponse {
  banks: BankAccount[]                          // 银行账户列表
  upis: Record<string, UpiWallet>              // UPI钱包 (按ID索引)
  kyc: number                                   // KYC状态
  url: string                                   // 客服URL
}

export interface DrawInfo {
  uid: number           // 用户ID
  dcoin: number         // 可提现金额
  limit: number         // 提现限制
  mincoin: number       // 最小提现金额
  maxcoin: number       // 最大提现金额
  memo: string          // 提示信息 (HTML格式)
  popmsg: string        // 弹窗消息
  customer: any[]       // 客服信息
  url: string           // 客服URL
}

// API端点常量
export const API_ENDPOINTS = {
  // 基础服务域名
  BASE_URL: 'http://service.haiwailaba.cyou',
  
  // 用户相关
  USER_BALANCE: '/user/balance',                    // 获取用户余额和支付渠道
  USER_BALANCE_WITH_PARAMS: '/user/balance?cat=2&coin=200&paypop=0', // 充值页面余额
  USER_BANKS: '/user/banks',                        // 获取用户银行账户
  USER_BANKS_SPLIT: '/user/banks?split=1',         // 获取银行账户和UPI钱包
  USER_HISTORY: '/user/history',                   // 获取交易历史
  
  // 交易相关
  DRAW_INDEX: '/draw/index',                       // 提现相关信息
  DRAW_INDEX_WITH_PARAMS: '/draw/index?drawpop=0', // 提现页面信息
  
  // 分页参数
  HISTORY_PAGINATION: '?num=1&size=10',            // 交易历史分页
  
  // 文件上传 (推测)
  UPLOAD_FILE: '/upload'
} as const

// 状态码映射
export const STATUS_CODES = {
  SUCCESS: 0,
  ERROR: 1
} as const

// 交易状态映射
export const TRANSACTION_STATUS = {
  PENDING: 0,     // 处理中
  SUCCESS: 1,     // 成功
  FAILED: 2,      // 失败
  REFUND: 3       // 退款
} as const

// KYC状态映射
export const KYC_STATUS = {
  NOT_VERIFIED: 0,  // 未验证
  VERIFIED: 1       // 已验证
} as const
