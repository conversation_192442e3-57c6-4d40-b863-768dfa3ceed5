<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader :title="$t('payment.title')" :show-back-button="true" />

    <!-- Loading State -->
    <div v-if="paymentStore.loading" class="flex items-center justify-center py-8">
      <LoadingSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="paymentStore.error" class="p-4">
      <ErrorMessage :message="paymentStore.error" @retry="handleRetry" />
    </div>

    <!-- Main Content -->
    <div v-else class="pb-6">
      <!-- Bank Accounts Section -->
      <div class="p-4">
        <div class="bg-white rounded-xl shadow-sm">
          <!-- Section Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Building2 class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">
                  {{ $t('payment.myBankAccounts') }}
                </h3>
                <p class="text-sm text-gray-600">Manage your bank accounts for withdrawals</p>
              </div>
            </div>
            <ActionButton
              :text="$t('payment.verifyNewBankAccount')"
              variant="outline"
              size="sm"
              icon="plus"
              @click="navigateToAddBank"
            />
          </div>

          <!-- Bank Accounts List -->
          <div class="p-6">
            <div v-if="paymentStore.bankAccounts.length > 0" class="space-y-4">
              <BankAccountCard
                v-for="account in paymentStore.bankAccounts"
                :key="account.id"
                :account="account"
                :show-actions="true"
                @edit="editBankAccount"
                @delete="deleteBankAccount"
              />
            </div>

            <!-- No Bank Accounts -->
            <div v-else class="text-center py-8">
              <div
                class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Building2 class="w-8 h-8 text-gray-400" />
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">No bank accounts added</h4>
              <p class="text-gray-600 mb-4">Add a bank account to enable withdrawals</p>
              <ActionButton
                :text="$t('payment.verifyNewBankAccount')"
                variant="primary"
                @click="navigateToAddBank"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- UPI Wallets Section -->
      <div class="p-4">
        <div class="bg-white rounded-xl shadow-sm">
          <!-- Section Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <Smartphone class="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">
                  {{ $t('payment.myWallets') }}
                </h3>
                <p class="text-sm text-gray-600">Link your UPI wallets for quick payments</p>
              </div>
            </div>
          </div>

          <!-- UPI Wallets List -->
          <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <UpiWalletCard
                v-for="(wallet, id) in paymentStore.upiWallets"
                :key="id"
                :wallet="wallet"
                @link="linkWallet"
                @unlink="unlinkWallet"
              />
            </div>

            <!-- No UPI Wallets -->
            <div v-if="Object.keys(paymentStore.upiWallets).length === 0" class="text-center py-8">
              <div
                class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Smartphone class="w-8 h-8 text-gray-400" />
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">No UPI wallets linked</h4>
              <p class="text-gray-600">Link your UPI wallets for faster payments</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Help Section -->
      <div class="p-4">
        <div class="bg-blue-50 rounded-xl p-4">
          <div class="flex items-start">
            <HelpCircle class="w-5 h-5 text-blue-600 mr-3 mt-0.5" />
            <div class="flex-1">
              <h4 class="text-sm font-medium text-blue-900 mb-1">Need help with payments?</h4>
              <p class="text-sm text-blue-700 mb-3">
                Contact our support team for assistance with payment methods
              </p>
              <ActionButton
                text="Contact Support"
                variant="outline"
                size="sm"
                @click="openSupport"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { usePaymentStore } from '@/stores/payment'
import type { BankAccount, UpiWallet } from '@/types/api'
import { Building2, Smartphone, HelpCircle } from 'lucide-vue-next'

// Components
import AppHeader from '@/components/layout/AppHeader.vue'
import BankAccountCard from '@/components/withdraw/BankAccountCard.vue'
import UpiWalletCard from '@/components/payment/UpiWalletCard.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

const router = useRouter()
const { t } = useI18n()
const paymentStore = usePaymentStore()

// Methods
const navigateToAddBank = () => {
  router.push('/pages/deposit/addcreditcard/addcreditcard')
}

const editBankAccount = (account: BankAccount) => {
  // TODO: Implement edit bank account functionality
  console.log('Edit bank account:', account)
}

const deleteBankAccount = (account: BankAccount) => {
  // TODO: Implement delete bank account functionality
  console.log('Delete bank account:', account)
}

const linkWallet = async (wallet: UpiWallet) => {
  try {
    await paymentStore.linkWallet({
      type: wallet.cat,
      identifier: wallet.name
    })
  } catch (error) {
    console.error('Failed to link wallet:', error)
  }
}

const unlinkWallet = (wallet: UpiWallet) => {
  // TODO: Implement unlink wallet functionality
  console.log('Unlink wallet:', wallet)
}

const openSupport = () => {
  // TODO: Open support chat or page
  console.log('Open support')
}

const handleRetry = () => {
  paymentStore.clearError()
  loadData()
}

const loadData = async () => {
  await paymentStore.fetchPaymentMethods()
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
