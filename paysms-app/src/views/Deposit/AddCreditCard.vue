<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader :title="$t('bankAccount.title')" :show-back-button="true" />

    <!-- Main Content -->
    <div class="pb-20">
      <!-- Important Notice -->
      <div class="p-4">
        <div class="bg-orange-50 border border-orange-200 rounded-xl p-4">
          <div class="flex items-start">
            <AlertTriangle class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
            <div>
              <h4 class="text-sm font-medium text-orange-900 mb-1">
                {{ $t('bankAccount.important') }}
              </h4>
              <div class="text-sm text-orange-700 space-y-1">
                <p>{{ $t('bankAccount.reviewDetails') }}</p>
                <p>{{ $t('bankAccount.cannotChange') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bank Account Form -->
      <div class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Account Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bankAccount.accountNumber') }} *
              </label>
              <input
                v-model="formData.accountNumber"
                type="text"
                :placeholder="$t('bankAccount.enterAccountNumber')"
                :class="[
                  'w-full px-4 py-3 border rounded-xl transition-colors',
                  errors.accountNumber
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                ]"
                @blur="validateField('accountNumber')"
              />
              <p v-if="errors.accountNumber" class="mt-1 text-sm text-red-600">
                {{ errors.accountNumber }}
              </p>
            </div>

            <!-- Confirm Account Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bankAccount.retypeAccountNumber') }} *
              </label>
              <input
                v-model="formData.confirmAccountNumber"
                type="text"
                :placeholder="$t('bankAccount.confirmAccountNumber')"
                :class="[
                  'w-full px-4 py-3 border rounded-xl transition-colors',
                  errors.confirmAccountNumber
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                ]"
                @blur="validateField('confirmAccountNumber')"
              />
              <p v-if="errors.confirmAccountNumber" class="mt-1 text-sm text-red-600">
                {{ errors.confirmAccountNumber }}
              </p>
            </div>

            <!-- IFSC Code -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bankAccount.ifscCode') }} *
              </label>
              <input
                v-model="formData.ifscCode"
                type="text"
                :placeholder="$t('bankAccount.enterIfscCode')"
                :class="[
                  'w-full px-4 py-3 border rounded-xl transition-colors',
                  errors.ifscCode
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                ]"
                @blur="validateField('ifscCode')"
                @input="handleIfscInput"
              />
              <p v-if="errors.ifscCode" class="mt-1 text-sm text-red-600">
                {{ errors.ifscCode }}
              </p>
              <p v-if="bankInfo" class="mt-1 text-sm text-green-600">
                {{ bankInfo }}
              </p>
            </div>

            <!-- Account Holder Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bankAccount.name') }} *
              </label>
              <input
                v-model="formData.accountHolderName"
                type="text"
                placeholder="Enter account holder name"
                :class="[
                  'w-full px-4 py-3 border rounded-xl transition-colors',
                  errors.accountHolderName
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                ]"
                @blur="validateField('accountHolderName')"
              />
              <p v-if="errors.accountHolderName" class="mt-1 text-sm text-red-600">
                {{ errors.accountHolderName }}
              </p>
            </div>

            <!-- Bank Proof Upload -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bankAccount.bankProof') }} *
              </label>
              <FileUpload
                v-model="formData.bankProof"
                accept="image/*,.pdf"
                :max-size="5"
                @upload="handleFileUpload"
                @error="handleFileError"
              />
              <p v-if="errors.bankProof" class="mt-1 text-sm text-red-600">
                {{ errors.bankProof }}
              </p>
              <p class="mt-1 text-xs text-gray-500">
                Upload bank statement, passbook, or cancelled cheque (Max 5MB)
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <ActionButton
        :text="$t('bankAccount.submitDetails')"
        :loading="isSubmitting"
        :disabled="!isFormValid"
        size="lg"
        class="w-full"
        @click="handleSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { usePaymentStore } from '@/stores/payment'
import { AlertTriangle } from 'lucide-vue-next'

// Components
import AppHeader from '@/components/layout/AppHeader.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import ActionButton from '@/components/common/ActionButton.vue'

const router = useRouter()
const { t } = useI18n()
const paymentStore = usePaymentStore()

// State
const isSubmitting = ref(false)
const bankInfo = ref('')

// Form data
const formData = reactive({
  accountNumber: '',
  confirmAccountNumber: '',
  ifscCode: '',
  accountHolderName: '',
  bankProof: null as File | null
})

// Form errors
const errors = reactive({
  accountNumber: '',
  confirmAccountNumber: '',
  ifscCode: '',
  accountHolderName: '',
  bankProof: ''
})

// Computed
const isFormValid = computed(() => {
  return (
    formData.accountNumber &&
    formData.confirmAccountNumber &&
    formData.ifscCode &&
    formData.accountHolderName &&
    formData.bankProof &&
    !Object.values(errors).some((error) => error)
  )
})

// Methods
const validateField = (field: keyof typeof formData) => {
  errors[field] = ''

  switch (field) {
    case 'accountNumber':
      if (!formData.accountNumber) {
        errors.accountNumber = 'Account number is required'
      } else if (!/^\d{9,18}$/.test(formData.accountNumber)) {
        errors.accountNumber = 'Account number must be 9-18 digits'
      }
      break

    case 'confirmAccountNumber':
      if (!formData.confirmAccountNumber) {
        errors.confirmAccountNumber = 'Please confirm your account number'
      } else if (formData.confirmAccountNumber !== formData.accountNumber) {
        errors.confirmAccountNumber = 'Account numbers do not match'
      }
      break

    case 'ifscCode':
      if (!formData.ifscCode) {
        errors.ifscCode = 'IFSC code is required'
      } else if (!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(formData.ifscCode.toUpperCase())) {
        errors.ifscCode = 'Invalid IFSC code format'
      }
      break

    case 'accountHolderName':
      if (!formData.accountHolderName) {
        errors.accountHolderName = t('bankAccount.nameRequired')
      } else if (formData.accountHolderName.length < 2) {
        errors.accountHolderName = t('bankAccount.nameMinLength')
      }
      break

    case 'bankProof':
      if (!formData.bankProof) {
        errors.bankProof = t('bankAccount.proofRequired')
      }
      break
  }
}

const handleIfscInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  formData.ifscCode = target.value.toUpperCase()

  // Mock bank info lookup
  if (formData.ifscCode.length === 11) {
    setTimeout(() => {
      bankInfo.value = t('bankAccount.sampleBankInfo')
    }, 500)
  } else {
    bankInfo.value = ''
  }
}

const handleFileUpload = (file: File) => {
  formData.bankProof = file
  errors.bankProof = ''
}

const handleFileError = (error: string) => {
  errors.bankProof = error
}

const handleSubmit = async () => {
  // Validate all fields
  Object.keys(formData).forEach((field) => {
    validateField(field as keyof typeof formData)
  })

  if (!isFormValid.value) {
    return
  }

  try {
    isSubmitting.value = true

    const bankAccountData = {
      accountNumber: formData.accountNumber,
      ifscCode: formData.ifscCode,
      accountHolderName: formData.accountHolderName,
      bankName: bankInfo.value || 'Unknown Bank'
    }

    await paymentStore.addNewBankAccount(bankAccountData)

    // Navigate back to payment management
    router.push('/pages/deposit/managepayment/managepayment')
  } catch (error) {
    console.error('Failed to add bank account:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>
