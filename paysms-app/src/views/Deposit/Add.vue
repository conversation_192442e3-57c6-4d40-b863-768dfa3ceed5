<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader :title="$t('deposit.title')" :show-back-button="true" />

    <!-- Loading State -->
    <div
      v-if="userStore.loading || paymentStore.loading"
      class="flex items-center justify-center py-8"
    >
      <LoadingSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="userStore.error || paymentStore.error" class="p-4">
      <ErrorMessage :message="userStore.error || paymentStore.error || ''" @retry="handleRetry" />
    </div>

    <!-- Main Content -->
    <div v-else class="pb-20">
      <!-- Current Balance Section -->
      <div class="p-4">
        <BalanceCard
          :title="$t('deposit.currentBalance')"
          :amount="userStore.totalBalance"
          class="bg-white border border-gray-200"
        >
          <template #subtitle>
            <div class="mt-2 flex justify-between text-sm text-gray-600">
              <span
                >{{ $t('deposit.cashBalance') }}:
                {{ formatCurrency(userStore.unutilizedAmount) }}</span
              >
              <span>{{ $t('deposit.cashBonus') }}: {{ formatCurrency(userStore.cashBonus) }}</span>
            </div>
          </template>
        </BalanceCard>
      </div>

      <!-- Amount Input Section -->
      <div class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            {{ $t('common.amount') }}
          </h3>

          <!-- Amount Input -->
          <AmountInput
            v-model="selectedAmount"
            :min="currentLimits.min"
            :max="currentLimits.max"
            :currency="'₹'"
            @update:model-value="handleAmountChange"
          />

          <!-- Quick Amount Buttons -->
          <div class="grid grid-cols-3 gap-3 mt-4">
            <button
              v-for="amount in quickAmounts"
              :key="amount"
              @click="selectQuickAmount(amount)"
              :class="[
                'py-3 px-4 rounded-lg border text-sm font-medium transition-colors',
                selectedAmount === amount
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white text-gray-700 hover:bg-gray-50'
              ]"
            >
              {{ formatCurrency(amount) }}
            </button>
          </div>
        </div>
      </div>

      <!-- Payment Method Section -->
      <div class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            {{ $t('deposit.paymentMethod') }}
          </h3>

          <PaymentMethodSelector
            v-model="selectedPaymentMethod"
            :payment-channels="paymentStore.paymentChannels"
            @update:model-value="handlePaymentMethodChange"
          />
        </div>
      </div>

      <!-- Payment Channel Section -->
      <div v-if="selectedPaymentMethod && availableChannels.length > 0" class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            {{ $t('deposit.paymentChannel') }}
          </h3>

          <PaymentChannelSelector
            v-model="selectedPaymentChannel"
            :channels="availableChannels"
            :selected-amount="selectedAmount"
            @update:model-value="handleChannelChange"
          />
        </div>
      </div>

      <!-- Tips Section -->
      <div v-if="balanceData?.memo" class="p-4">
        <div class="bg-blue-50 rounded-xl p-4">
          <h4 class="text-sm font-medium text-blue-900 mb-2">
            {{ $t('deposit.tips') }}
          </h4>
          <div class="text-sm text-blue-700" v-html="balanceData.memo" />
        </div>
      </div>

      <!-- Deposit Button -->
      <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <ActionButton
          :text="$t('deposit.depositNow')"
          :loading="isProcessing"
          :disabled="!canDeposit"
          size="lg"
          class="w-full"
          @click="handleDeposit"
        />

        <!-- Amount Summary -->
        <div v-if="selectedAmount > 0" class="mt-2 text-center">
          <p class="text-sm text-gray-600">
            {{ $t('deposit.depositUsuallyCredited') }}
          </p>
          <p v-if="totalAmount !== selectedAmount" class="text-xs text-gray-500 mt-1">
            Total: {{ formatCurrency(totalAmount) }} (including fees)
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { usePaymentStore } from '@/stores/payment'
import { useTransactionStore } from '@/stores/transaction'
import { formatCurrency } from '@/utils/currency'
import type { PaymentChannel, PaymentPage, BalanceResponse } from '@/types/api'

// Components
import AppHeader from '@/components/layout/AppHeader.vue'
import BalanceCard from '@/components/balance/BalanceCard.vue'
import AmountInput from '@/components/deposit/AmountInput.vue'
import PaymentMethodSelector from '@/components/deposit/PaymentMethodSelector.vue'
import PaymentChannelSelector from '@/components/deposit/PaymentChannelSelector.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()
const paymentStore = usePaymentStore()
const transactionStore = useTransactionStore()

// State
const selectedAmount = ref(0)
const selectedPaymentMethod = ref<PaymentChannel | null>(null)
const selectedPaymentChannel = ref<PaymentPage | null>(null)
const isProcessing = ref(false)
const balanceData = ref<BalanceResponse | null>(null)

// Quick amount options
const quickAmounts = [100, 500, 1000, 2000, 5000, 10000]

// Computed properties
const currentLimits = computed(() => {
  if (!selectedPaymentMethod.value) {
    return { min: 100, max: 1000000 }
  }
  return {
    min: selectedPaymentMethod.value.mincoin,
    max: selectedPaymentMethod.value.maxcoin
  }
})

const availableChannels = computed(() => {
  return selectedPaymentMethod.value?.pages || []
})

const totalAmount = computed(() => {
  if (!selectedPaymentChannel.value || selectedAmount.value === 0) {
    return selectedAmount.value
  }

  const channel = selectedPaymentChannel.value
  const fee = channel.rate > 0 ? selectedAmount.value * channel.rate : channel.discoin
  return selectedAmount.value + fee
})

const canDeposit = computed(() => {
  return (
    selectedAmount.value > 0 &&
    selectedPaymentMethod.value &&
    selectedPaymentChannel.value &&
    selectedAmount.value >= currentLimits.value.min &&
    selectedAmount.value <= currentLimits.value.max
  )
})

// Methods
const handleAmountChange = (amount: number) => {
  selectedAmount.value = amount
}

const selectQuickAmount = (amount: number) => {
  selectedAmount.value = amount
}

const handlePaymentMethodChange = (method: PaymentChannel | null) => {
  selectedPaymentMethod.value = method
  selectedPaymentChannel.value = null

  // Auto-select first channel if available
  if (method && method.pages.length > 0) {
    selectedPaymentChannel.value = method.pages[0]
  }
}

const handleChannelChange = (channel: PaymentPage | null) => {
  selectedPaymentChannel.value = channel
}

const handleDeposit = async () => {
  if (!canDeposit.value) return

  try {
    isProcessing.value = true

    const { useToast } = await import('@/composables/useToast')
    const { success: showSuccess } = useToast()

    const depositData = {
      amount: selectedAmount.value,
      channelId: selectedPaymentMethod.value!.id,
      pageId: selectedPaymentChannel.value!.id
    }

    await transactionStore.makeDeposit(depositData)

    // Show success message and redirect
    showSuccess(
      t('deposit.depositSubmitted', { amount: formatCurrency(selectedAmount.value) }),
      t('deposit.depositSuccess')
    )

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/pages/transaction/transaction')
    }, 1500)
  } catch (error) {
    const { useToast } = await import('@/composables/useToast')
    const { error: showError } = useToast()

    const errorMessage = error instanceof Error ? error.message : t('deposit.depositFailed')
    showError(errorMessage, t('deposit.depositError'))
    console.error('Deposit failed:', error)
  } finally {
    isProcessing.value = false
  }
}

const handleRetry = () => {
  userStore.clearError()
  paymentStore.clearError()
  loadData()
}

const loadData = async () => {
  await Promise.all([userStore.fetchBalance(), paymentStore.fetchPaymentMethods()])

  // Get deposit balance data with payment channels
  try {
    const depositBalance = await userStore.fetchDepositBalance(selectedAmount.value || 200)
    if (depositBalance) {
      balanceData.value = depositBalance
      paymentStore.setPaymentChannels(depositBalance.channels)
    }
  } catch (error) {
    console.error('Failed to load deposit balance:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadData()
})

// Watch for amount changes to update limits
watch(selectedAmount, (newAmount) => {
  if (newAmount > 0 && balanceData.value) {
    // Could fetch updated balance data with new amount
  }
})
</script>
