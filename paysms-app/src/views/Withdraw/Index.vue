<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader :title="$t('withdraw.title')" :show-back-button="true" />

    <!-- Loading State -->
    <div
      v-if="userStore.loading || transactionStore.loading"
      class="flex items-center justify-center py-8"
    >
      <LoadingSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="userStore.error || transactionStore.error" class="p-4">
      <ErrorMessage
        :message="userStore.error || transactionStore.error || ''"
        @retry="handleRetry"
      />
    </div>

    <!-- Main Content -->
    <div v-else class="pb-20">
      <!-- Withdrawable Balance Section -->
      <div class="p-4">
        <BalanceCard
          :title="$t('withdraw.withdrawableBalance')"
          :amount="transactionStore.withdrawableAmount"
          :is-primary="true"
          class="bg-gradient-to-r from-green-500 to-emerald-600 text-white"
        >
          <template #subtitle>
            <div class="mt-2 text-white/90 text-sm">
              {{ $t('withdraw.withdrawUsuallyProcessed') }}
            </div>
          </template>
        </BalanceCard>
      </div>

      <!-- Bank Accounts Section -->
      <div class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ $t('payment.myBankAccounts') }}
            </h3>
            <ActionButton
              :text="$t('withdraw.addNewBankAccount')"
              variant="outline"
              size="sm"
              icon="plus"
              @click="navigateToAddBank"
            />
          </div>

          <!-- Bank Accounts List -->
          <div v-if="paymentStore.bankAccounts.length > 0" class="space-y-3">
            <BankAccountCard
              v-for="account in paymentStore.bankAccounts"
              :key="account.id"
              :account="account"
              :selected="selectedBankAccount?.id === account.id"
              @select="selectBankAccount"
            />
          </div>

          <!-- No Bank Accounts -->
          <div v-else class="text-center py-8">
            <div
              class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <CreditCard class="w-8 h-8 text-gray-400" />
            </div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">
              {{ $t('withdraw.addNewBankAccount') }}
            </h4>
            <p class="text-gray-600 mb-4">
              {{ $t('withdraw.addBankAccountDesc') }}
            </p>
            <ActionButton
              :text="$t('withdraw.addNewBankAccount')"
              variant="primary"
              @click="navigateToAddBank"
            />
          </div>
        </div>
      </div>

      <!-- Withdraw Amount Section -->
      <div v-if="selectedBankAccount" class="p-4">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            {{ $t('common.amount') }}
          </h3>

          <!-- Amount Input -->
          <AmountInput
            v-model="withdrawAmount"
            :min="transactionStore.withdrawLimits.min"
            :max="
              Math.min(transactionStore.withdrawLimits.max, transactionStore.withdrawableAmount)
            "
            :currency="'₹'"
            @update:model-value="handleAmountChange"
          />

          <!-- Quick Amount Buttons -->
          <div class="grid grid-cols-3 gap-3 mt-4">
            <button
              v-for="amount in quickWithdrawAmounts"
              :key="amount"
              @click="selectQuickAmount(amount)"
              :disabled="amount > transactionStore.withdrawableAmount"
              :class="[
                'py-3 px-4 rounded-lg border text-sm font-medium transition-colors',
                withdrawAmount === amount
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : amount > transactionStore.withdrawableAmount
                  ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'border-gray-200 bg-white text-gray-700 hover:bg-gray-50'
              ]"
            >
              {{ formatCurrency(amount) }}
            </button>
          </div>
        </div>
      </div>

      <!-- Tips Section -->
      <div v-if="drawInfo?.memo" class="p-4">
        <div class="bg-green-50 rounded-xl p-4">
          <h4 class="text-sm font-medium text-green-900 mb-2">
            {{ $t('deposit.tips') }}
          </h4>
          <div class="text-sm text-green-700" v-html="drawInfo.memo" />
        </div>
      </div>

      <!-- Withdraw Button -->
      <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <ActionButton
          :text="$t('withdraw.withdrawNow')"
          :loading="isProcessing"
          :disabled="!canWithdraw"
          size="lg"
          class="w-full"
          @click="handleWithdraw"
        />

        <!-- Amount Summary -->
        <div v-if="withdrawAmount > 0 && selectedBankAccount" class="mt-2 text-center">
          <p class="text-sm text-gray-600">
            {{ $t('withdraw.withdrawUsuallyProcessed') }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            To: {{ selectedBankAccount.bankName }} ****{{
              selectedBankAccount.accountNumber.slice(-4)
            }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { usePaymentStore } from '@/stores/payment'
import { useTransactionStore } from '@/stores/transaction'
import { formatCurrency } from '@/utils/currency'
import type { BankAccount, DrawInfo } from '@/types/api'
import { CreditCard } from 'lucide-vue-next'

// Components
import AppHeader from '@/components/layout/AppHeader.vue'
import BalanceCard from '@/components/balance/BalanceCard.vue'
import AmountInput from '@/components/deposit/AmountInput.vue'
import BankAccountCard from '@/components/withdraw/BankAccountCard.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()
const paymentStore = usePaymentStore()
const transactionStore = useTransactionStore()

// State
const withdrawAmount = ref(0)
const selectedBankAccount = ref<BankAccount | null>(null)
const isProcessing = ref(false)
const drawInfo = ref<DrawInfo | null>(null)

// Quick withdraw amounts
const quickWithdrawAmounts = computed(() => {
  const maxAmount = transactionStore.withdrawableAmount
  const amounts = [500, 1000, 2000, 5000, 10000, 20000]
  return amounts.filter((amount) => amount <= maxAmount)
})

// Computed properties
const canWithdraw = computed(() => {
  return (
    withdrawAmount.value > 0 &&
    selectedBankAccount.value &&
    withdrawAmount.value >= transactionStore.withdrawLimits.min &&
    withdrawAmount.value <=
      Math.min(transactionStore.withdrawLimits.max, transactionStore.withdrawableAmount)
  )
})

// Methods
const handleAmountChange = (amount: number) => {
  withdrawAmount.value = amount
}

const selectQuickAmount = (amount: number) => {
  if (amount <= transactionStore.withdrawableAmount) {
    withdrawAmount.value = amount
  }
}

const selectBankAccount = (account: BankAccount) => {
  selectedBankAccount.value = account
}

const navigateToAddBank = () => {
  router.push('/pages/deposit/addcreditcard/addcreditcard')
}

const handleWithdraw = async () => {
  if (!canWithdraw.value || !selectedBankAccount.value) return

  try {
    isProcessing.value = true

    const withdrawData = {
      amount: withdrawAmount.value,
      bankId: selectedBankAccount.value.id
    }

    await transactionStore.makeWithdraw(withdrawData)

    // Show success message and redirect
    router.push('/pages/transaction/transaction')
  } catch (error) {
    console.error('Withdraw failed:', error)
  } finally {
    isProcessing.value = false
  }
}

const handleRetry = () => {
  userStore.clearError()
  transactionStore.clearError()
  loadData()
}

const loadData = async () => {
  await Promise.all([
    userStore.fetchBalance(),
    paymentStore.fetchPaymentMethods(),
    transactionStore.fetchDrawInfo()
  ])

  drawInfo.value = transactionStore.drawInfo

  // Auto-select first bank account if available
  if (paymentStore.bankAccounts.length > 0) {
    selectedBankAccount.value = paymentStore.bankAccounts[0]
  }
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
