// utils/translationChecker.ts - 翻译完整性检查工具
import zhCN from '@/locales/zh-CN'
import enUS from '@/locales/en-US'
import hiIN from '@/locales/hi-IN'
import ptBR from '@/locales/pt-BR'

type TranslationObject = Record<string, any>

// 获取所有翻译键的扁平化列表
function flattenKeys(obj: TranslationObject, prefix = ''): string[] {
  const keys: string[] = []
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key
    
    if (typeof value === 'object' && value !== null) {
      keys.push(...flattenKeys(value, fullKey))
    } else {
      keys.push(fullKey)
    }
  }
  
  return keys
}

// 检查翻译完整性
export function checkTranslationCompleteness() {
  const locales = {
    'zh-CN': zhCN,
    'en-US': enUS,
    'hi-IN': hiIN,
    'pt-BR': ptBR
  }
  
  // 获取所有语言的键
  const allKeys = Object.entries(locales).reduce((acc, [locale, translations]) => {
    acc[locale] = flattenKeys(translations)
    return acc
  }, {} as Record<string, string[]>)
  
  // 以中文为基准，检查其他语言的缺失键
  const baseKeys = allKeys['zh-CN']
  const missingKeys: Record<string, string[]> = {}
  
  Object.entries(allKeys).forEach(([locale, keys]) => {
    if (locale === 'zh-CN') return
    
    const missing = baseKeys.filter(key => !keys.includes(key))
    if (missing.length > 0) {
      missingKeys[locale] = missing
    }
  })
  
  // 检查多余的键（其他语言有但中文没有的）
  const extraKeys: Record<string, string[]> = {}
  
  Object.entries(allKeys).forEach(([locale, keys]) => {
    if (locale === 'zh-CN') return
    
    const extra = keys.filter(key => !baseKeys.includes(key))
    if (extra.length > 0) {
      extraKeys[locale] = extra
    }
  })
  
  return {
    baseKeys,
    missingKeys,
    extraKeys,
    totalKeys: baseKeys.length,
    isComplete: Object.keys(missingKeys).length === 0 && Object.keys(extraKeys).length === 0
  }
}

// 检查硬编码文本
export function findHardcodedText(content: string): string[] {
  const hardcodedPatterns = [
    // 英文硬编码模式
    /['"`]([A-Z][a-z]+ [a-z]+.*?)['"`]/g,
    /['"`]([A-Z][a-z]+)['"`]/g,
    // 中文硬编码模式
    /['"`]([\u4e00-\u9fa5]+.*?)['"`]/g,
    // 常见的硬编码短语
    /['"`](Loading\.\.\.|Failed to load|Click to upload|Uploading\.\.\.|Success|Error|Cancel|Submit)['"`]/g
  ]
  
  const found: string[] = []
  
  hardcodedPatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1]
      // 排除一些明显不是用户可见文本的内容
      if (!text.match(/^[a-z-]+$/) && // CSS类名
          !text.match(/^\d+$/) && // 纯数字
          !text.match(/^[A-Z_]+$/) && // 常量
          !text.includes('{{') && // Vue模板
          !text.includes('$t(') // 已经使用翻译的
      ) {
        found.push(text)
      }
    }
  })
  
  return [...new Set(found)] // 去重
}

// 生成翻译报告
export function generateTranslationReport() {
  const completeness = checkTranslationCompleteness()
  
  console.group('🌍 翻译完整性报告')
  console.log(`📊 总翻译键数量: ${completeness.totalKeys}`)
  console.log(`✅ 翻译完整性: ${completeness.isComplete ? '完整' : '不完整'}`)
  
  if (Object.keys(completeness.missingKeys).length > 0) {
    console.group('❌ 缺失的翻译键:')
    Object.entries(completeness.missingKeys).forEach(([locale, keys]) => {
      console.log(`${locale}: ${keys.length} 个缺失`)
      keys.forEach(key => console.log(`  - ${key}`))
    })
    console.groupEnd()
  }
  
  if (Object.keys(completeness.extraKeys).length > 0) {
    console.group('⚠️ 多余的翻译键:')
    Object.entries(completeness.extraKeys).forEach(([locale, keys]) => {
      console.log(`${locale}: ${keys.length} 个多余`)
      keys.forEach(key => console.log(`  - ${key}`))
    })
    console.groupEnd()
  }
  
  console.groupEnd()
  
  return completeness
}

// 验证翻译键是否存在
export function validateTranslationKey(key: string, locale = 'zh-CN'): boolean {
  const locales = {
    'zh-CN': zhCN,
    'en-US': enUS,
    'hi-IN': hiIN,
    'pt-BR': ptBR
  }
  
  const translations = locales[locale as keyof typeof locales]
  if (!translations) return false
  
  const keys = key.split('.')
  let current: any = translations
  
  for (const k of keys) {
    if (typeof current !== 'object' || current === null || !(k in current)) {
      return false
    }
    current = current[k]
  }
  
  return typeof current === 'string'
}

// 开发环境下的翻译检查
if (import.meta.env.DEV) {
  // 在开发环境下自动运行翻译检查
  setTimeout(() => {
    generateTranslationReport()
  }, 1000)
}
