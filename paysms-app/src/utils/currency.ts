// utils/currency.ts - 货币格式化工具函数

/**
 * 格式化货币显示
 * @param amount 金额
 * @param currency 货币符号，默认为印度卢比
 * @param locale 本地化设置，默认为印度
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(
  amount: number, 
  currency = '₹', 
  locale = 'en-IN'
): string {
  return `${currency}${amount.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })}`
}

/**
 * 解析货币字符串为数字
 * @param value 货币字符串
 * @returns 解析后的数字
 */
export function parseCurrency(value: string): number {
  return parseFloat(value.replace(/[^\d.-]/g, '')) || 0
}

/**
 * 格式化金额显示（带小数点）
 * @param amount 金额
 * @param currency 货币符号
 * @param locale 本地化设置
 * @returns 格式化后的货币字符串（总是显示2位小数）
 */
export function formatCurrencyWithDecimals(
  amount: number, 
  currency = '₹', 
  locale = 'en-IN'
): string {
  return `${currency}${amount.toLocaleString(locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

/**
 * 验证金额是否在指定范围内
 * @param amount 金额
 * @param min 最小值
 * @param max 最大值
 * @returns 验证结果
 */
export function validateAmount(amount: number, min: number, max: number): {
  isValid: boolean
  error?: string
} {
  if (amount < min) {
    return {
      isValid: false,
      error: `Amount must be at least ${formatCurrency(min)}`
    }
  }
  
  if (amount > max) {
    return {
      isValid: false,
      error: `Amount cannot exceed ${formatCurrency(max)}`
    }
  }
  
  return { isValid: true }
}

/**
 * 计算费用
 * @param amount 基础金额
 * @param rate 费率（小数形式，如0.03表示3%）
 * @param fixedFee 固定费用
 * @returns 计算后的费用
 */
export function calculateFee(amount: number, rate: number = 0, fixedFee: number = 0): number {
  return (amount * rate) + fixedFee
}

/**
 * 计算总金额（包含费用）
 * @param amount 基础金额
 * @param rate 费率
 * @param fixedFee 固定费用
 * @returns 总金额
 */
export function calculateTotalAmount(amount: number, rate: number = 0, fixedFee: number = 0): number {
  return amount + calculateFee(amount, rate, fixedFee)
}

/**
 * 格式化折扣显示
 * @param disrate 折扣率字符串
 * @param discoin 折扣金额
 * @returns 格式化后的折扣显示
 */
export function formatDiscount(disrate: string, discoin: number): string {
  if (disrate.includes('%')) {
    return disrate
  }
  return discoin > 0 ? `+${formatCurrency(discoin)}` : disrate
}
