// services/mockApi.ts - 模拟API服务（用于开发测试）
import type {
  BalanceResponse,
  TransactionHistory,
  BanksResponse,
  DrawInfo,
  UserInfo,
  PaymentChannel
} from '@/types/api'

// 模拟用户数据
const mockUserInfo: UserInfo = {
  uid: 10002,
  coin: 1250, // 总余额
  dcoin: 500, // 可提现余额
  ecoin: 750, // 已添加金额(未使用)
  bonus: 125, // 现金奖金
  totalcoin: 1250, // 总余额
  kyc: 1, // 已验证
  svip: 0,
  ispayer: 0,
  nodislabelid: 0
}

// 模拟支付渠道
const mockPaymentChannels: PaymentChannel[] = [
  {
    id: 10,
    title: 'AliPay',
    icon: 'https://img.yonogames.com//upload/********/9e2244e45601065efb0a9247cf29f689.png',
    subtype: 'onlinepay',
    mincoin: 100,
    maxcoin: 1000000,
    disrate: '+500',
    discoin: 500,
    type: 0,
    pages: [
      {
        id: 10,
        title: 'test1009',
        type: 1,
        banktype: 0,
        mincoin: 100,
        maxcoin: 1000000,
        disrate: '+500',
        discoin: 500,
        rate: 0
      }
    ]
  },
  {
    id: 17,
    title: 'upi',
    icon: 'https://img.yonogames.com/',
    subtype: 'onlinepay',
    mincoin: 200,
    maxcoin: 100000,
    disrate: '*****%',
    discoin: 0,
    type: 0,
    pages: [
      {
        id: 16,
        title: 'Paytm APP',
        type: 1,
        banktype: 0,
        mincoin: 200,
        maxcoin: 100000,
        disrate: '*****%',
        discoin: 0,
        rate: 0.03
      }
    ]
  }
]

// 模拟余额响应
const mockBalanceResponse: BalanceResponse = {
  user: mockUserInfo,
  channels: mockPaymentChannels,
  memo: 'Tips: Welcome to PaySMS<br/>Enjoy secure payments<br/>24/7 customer support',
  popmsg: '',
  customer: [],
  url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
}

// 模拟API延迟
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// 模拟API服务类
export class MockApiService {
  // 获取用户余额
  static async getUserBalance(): Promise<BalanceResponse> {
    await delay(800) // 模拟网络延迟
    console.log('Mock API: getUserBalance called')
    return mockBalanceResponse
  }

  // 获取充值页面余额
  static async getDepositBalance(coin: number = 200): Promise<BalanceResponse> {
    await delay(600)
    console.log('Mock API: getDepositBalance called with coin:', coin)
    return {
      ...mockBalanceResponse,
      memo: `Tips: Deposit is usually credited in minutes<br/>Minimum deposit: ₹100<br/>Maximum deposit: ₹1,000,000<br/>24/7 customer support available`,
      channels: [
        ...mockPaymentChannels,
        {
          id: 20,
          title: 'UPI',
          icon: 'https://img.yonogames.com/upi-icon.png',
          subtype: 'upi',
          mincoin: 200,
          maxcoin: 100000,
          disrate: '*****%',
          discoin: 0,
          type: 1,
          pages: [
            {
              id: 20,
              title: 'PhonePe',
              type: 1,
              banktype: 1,
              mincoin: 200,
              maxcoin: 50000,
              disrate: '*****%',
              discoin: 0,
              rate: 0.03
            },
            {
              id: 21,
              title: 'Google Pay',
              type: 1,
              banktype: 1,
              mincoin: 200,
              maxcoin: 50000,
              disrate: '*****%',
              discoin: 0,
              rate: 0.025
            },
            {
              id: 22,
              title: 'Paytm',
              type: 1,
              banktype: 1,
              mincoin: 200,
              maxcoin: 100000,
              disrate: '*****%',
              discoin: 0,
              rate: 0.035
            }
          ]
        }
      ]
    }
  }

  // 获取银行账户和钱包
  static async getBanksAndWallets(): Promise<BanksResponse> {
    await delay(500)
    console.log('Mock API: getBanksAndWallets called')
    return {
      banks: [],
      upis: {
        '1': {
          id: 1,
          name: 'Paytm',
          checked: true,
          card: 'Link',
          icon: '/static/img/paytm.png',
          cat: 1
        },
        '2': {
          id: 2,
          name: 'Phonepe',
          checked: true,
          card: 'Link',
          icon: '/static/img/phonepe.png',
          cat: 2
        },
        '3': {
          id: 3,
          name: 'Gpay',
          checked: true,
          card: 'Link',
          icon: '/static/img/gpay.png',
          cat: 3
        },
        '4': {
          id: 4,
          name: 'Other UPI ID',
          checked: true,
          card: 'Link',
          icon: '/static/img/upi.png',
          cat: 4
        }
      },
      kyc: 1,
      url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
    }
  }

  // 获取交易历史
  static async getTransactionHistory(
    page: number = 1,
    size: number = 10
  ): Promise<TransactionHistory> {
    await delay(700)
    console.log('Mock API: getTransactionHistory called with page:', page, 'size:', size)
    return {
      deplist: {
        '08/02/2025': [
          {
            id: 5,
            orderid: '202508021248350198519953',
            coin: '500.00',
            status_str: 'Success',
            status: 1,
            time: '2:30 pm',
            title: 'AliPay',
            memo: ''
          },
          {
            id: 4,
            orderid: '202508021148320156545054',
            coin: '300.00',
            status_str: 'In-Process',
            status: 0,
            time: '11:48 am',
            title: 'upi',
            memo: ''
          }
        ]
      },
      drawlist: [],
      betlist: [],
      bonuslist: [],
      banklist: [],
      show: 1,
      url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
    }
  }

  // 获取提现信息
  static async getDrawInfo(): Promise<DrawInfo> {
    await delay(400)
    console.log('Mock API: getDrawInfo called')
    return {
      uid: 10002,
      dcoin: 500,
      limit: 0,
      mincoin: 100,
      maxcoin: 1000000,
      memo: 'Withdraw instantly to your bank account<br/>Processing time: 5-10 minutes<br/>24/7 support available',
      popmsg: '',
      customer: [],
      url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
    }
  }
}

// 检查是否在开发环境中使用模拟API
export const shouldUseMockApi = () => {
  return false // 在开发环境中始终使用模拟API
}
