// services/api.ts - API服务层
import axios, { AxiosInstance, AxiosResponse } from 'axios'
import type {
  ApiResponse,
  BalanceResponse,
  TransactionHistory,
  BanksResponse,
  DrawInfo
} from '@/types/api'
import { MockApiService, shouldUseMockApi } from './mockApi'

class ApiService {
  private api: AxiosInstance

  constructor() {
    try {
      this.api = axios.create({
        baseURL: 'http://service.haiwailaba.cyou',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    } catch (error) {
      console.error('Failed to initialize API service:', error)
      throw error
    }

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证token等
        console.log('API Request:', config.method?.toUpperCase(), config.url)
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log('API Response:', response.status, response.data)
        return response
      },
      (error) => {
        console.error('API Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // 获取用户余额和支付渠道
  async getUserBalance(): Promise<BalanceResponse> {
    if (shouldUseMockApi()) {
      return MockApiService.getUserBalance()
    }
    if (!this.api) {
      throw new Error('API service not initialized')
    }
    const response = await this.api.get<ApiResponse<BalanceResponse>>('/user/balance')
    return response.data.data
  }

  // 获取充值页面的余额信息
  async getDepositBalance(coin: number = 200): Promise<BalanceResponse> {
    if (shouldUseMockApi()) {
      return MockApiService.getDepositBalance(coin)
    }
    const response = await this.api.get<ApiResponse<BalanceResponse>>(
      `/user/balance?cat=2&coin=${coin}&paypop=0`
    )
    return response.data.data
  }

  // 获取用户银行账户
  async getUserBanks(): Promise<BanksResponse> {
    const response = await this.api.get<ApiResponse<BanksResponse>>('/user/banks')
    return response.data.data
  }

  // 获取银行账户和UPI钱包
  async getBanksAndWallets(): Promise<BanksResponse> {
    if (shouldUseMockApi()) {
      return MockApiService.getBanksAndWallets()
    }
    if (!this.api) {
      throw new Error('API service not initialized')
    }
    const response = await this.api.get<ApiResponse<BanksResponse>>('/user/banks?split=1')
    return response.data.data
  }

  // 获取交易历史
  async getTransactionHistory(page: number = 1, size: number = 10): Promise<TransactionHistory> {
    const response = await this.api.get<ApiResponse<TransactionHistory>>(
      `/user/history?num=${page}&size=${size}`
    )
    return response.data.data
  }

  // 获取提现信息
  async getDrawInfo(): Promise<DrawInfo> {
    const response = await this.api.get<ApiResponse<DrawInfo>>('/draw/index?drawpop=0')
    return response.data.data
  }

  // 创建充值订单 (推测的API)
  async createDeposit(data: { amount: number; channelId: number; pageId: number }): Promise<any> {
    const response = await this.api.post('/deposit/create', data)
    return response.data.data
  }

  // 创建提现订单 (推测的API)
  async createWithdraw(data: { amount: number; bankId: number }): Promise<any> {
    const response = await this.api.post('/withdraw/create', data)
    return response.data.data
  }

  // 添加银行账户 (推测的API)
  async addBankAccount(data: {
    accountNumber: string
    ifscCode: string
    accountHolderName: string
    bankName: string
  }): Promise<any> {
    const response = await this.api.post('/user/banks/add', data)
    return response.data.data
  }

  // 链接UPI钱包 (推测的API)
  async linkUpiWallet(data: { type: number; identifier: string }): Promise<any> {
    const response = await this.api.post('/user/upi/link', data)
    return response.data.data
  }

  // 上传文件 (推测的API)
  async uploadFile(file: File): Promise<string> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data.data.url
  }
}

// 创建单例实例
export const apiService = new ApiService()

// 导出具体的API方法，方便在组件中使用
export const getUserBalance = () => apiService.getUserBalance()
export const getDepositBalance = (coin?: number) => apiService.getDepositBalance(coin)
export const getUserBanks = () => apiService.getUserBanks()
export const getBanksAndWallets = () => apiService.getBanksAndWallets()
export const getTransactionHistory = (page?: number, size?: number) =>
  apiService.getTransactionHistory(page, size)
export const getDrawInfo = () => apiService.getDrawInfo()
export const createDeposit = (data: { amount: number; channelId: number; pageId: number }) =>
  apiService.createDeposit(data)
export const createWithdraw = (data: { amount: number; bankId: number }) =>
  apiService.createWithdraw(data)
export const addBankAccount = (data: {
  accountNumber: string
  ifscCode: string
  accountHolderName: string
  bankName: string
}) => apiService.addBankAccount(data)
export const linkUpiWallet = (data: { type: number; identifier: string }) =>
  apiService.linkUpiWallet(data)
export const uploadFile = (file: File) => apiService.uploadFile(file)
