@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS 变量定义 */
:root {
  /* 主色调 */
  --primary: 214 100% 59%; /* #3b82f6 */
  --primary-foreground: 0 0% 100%; /* #ffffff */

  /* 次要色调 */
  --secondary: 210 40% 98%; /* #f8fafc */
  --secondary-foreground: 222 84% 5%; /* #0f172a */

  /* 背景色 */
  --background: 0 0% 100%; /* #ffffff */
  --foreground: 222 84% 5%; /* #0f172a */

  /* 卡片 */
  --card: 0 0% 100%; /* #ffffff */
  --card-foreground: 222 84% 5%; /* #0f172a */

  /* 弹出层 */
  --popover: 0 0% 100%; /* #ffffff */
  --popover-foreground: 222 84% 5%; /* #0f172a */

  /* 静音色 */
  --muted: 210 40% 96%; /* #f1f5f9 */
  --muted-foreground: 215 16% 47%; /* #64748b */

  /* 强调色 */
  --accent: 210 40% 96%; /* #f1f5f9 */
  --accent-foreground: 222 84% 5%; /* #0f172a */

  /* 破坏性操作 */
  --destructive: 0 84% 60%; /* #ef4444 */
  --destructive-foreground: 0 0% 100%; /* #ffffff */

  /* 边框和输入 */
  --border: 214 32% 91%; /* #e2e8f0 */
  --input: 214 32% 91%; /* #e2e8f0 */
  --ring: 214 100% 59%; /* #3b82f6 */

  /* 圆角 */
  --radius: 0.5rem;
}

/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
