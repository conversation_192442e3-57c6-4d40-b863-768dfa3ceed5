import type { RouteRecordRaw } from 'vue-router'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: 'My Balance' }
  },
  {
    path: '/pages/deposit/add/add',
    name: 'Deposit',
    component: () => import('@/views/Deposit/Add.vue'),
    meta: { title: 'Deposit' }
  },
  {
    path: '/pages/withdraw/index',
    name: 'Withdraw',
    component: () => import('@/views/Withdraw/Index.vue'),
    meta: { title: 'WITHDRAW' }
  },
  {
    path: '/pages/transaction/transaction',
    name: 'Transactions',
    component: () => import('@/views/Transaction/Transaction.vue'),
    meta: { title: 'My Transactions' }
  },
  {
    path: '/pages/deposit/managepayment/managepayment',
    name: 'ManagePayments',
    component: () => import('@/views/Deposit/ManagePayment.vue'),
    meta: { title: 'Manage Payments' }
  },
  {
    path: '/pages/deposit/addcreditcard/addcreditcard',
    name: 'AddBankAccount',
    component: () => import('@/views/Deposit/AddCreditCard.vue'),
    meta: { title: 'Add New Bank Account' }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue'),
    meta: { title: 'API Test' }
  }
]
