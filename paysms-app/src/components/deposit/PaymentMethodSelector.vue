<template>
  <div class="space-y-3">
    <div
      v-for="channel in paymentChannels"
      :key="channel.id"
      @click="selectMethod(channel)"
      :class="[
        'p-4 border rounded-xl cursor-pointer transition-all duration-200',
        isSelected(channel)
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
      ]"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <!-- Payment Method Icon -->
          <div class="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
            <img
              v-if="channel.icon"
              :src="channel.icon"
              :alt="channel.title"
              class="w-8 h-8 object-contain"
              @error="handleImageError"
            />
            <div v-else class="w-8 h-8 bg-gray-300 rounded flex items-center justify-center">
              <CreditCard class="w-4 h-4 text-gray-600" />
            </div>
          </div>
          
          <!-- Payment Method Info -->
          <div>
            <h4 class="font-medium text-gray-900">{{ channel.title }}</h4>
            <p class="text-sm text-gray-600">
              {{ formatCurrency(channel.mincoin) }} - {{ formatCurrency(channel.maxcoin) }}
            </p>
          </div>
        </div>
        
        <!-- Discount/Bonus Info -->
        <div class="text-right">
          <div v-if="channel.disrate" class="text-sm font-medium text-green-600">
            {{ formatDiscount(channel.disrate, channel.discoin) }}
          </div>
          <div class="text-xs text-gray-500">
            {{ channel.pages.length }} {{ channel.pages.length === 1 ? 'option' : 'options' }}
          </div>
        </div>
      </div>
      
      <!-- Selection Indicator -->
      <div
        v-if="isSelected(channel)"
        class="mt-3 pt-3 border-t border-blue-200"
      >
        <div class="flex items-center text-blue-600">
          <Check class="w-4 h-4 mr-2" />
          <span class="text-sm font-medium">Selected</span>
        </div>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-if="paymentChannels.length === 0" class="text-center py-8">
      <CreditCard class="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <p class="text-gray-600">No payment methods available</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CreditCard, Check } from 'lucide-vue-next'
import { formatCurrency } from '@/utils/currency'
import type { PaymentChannel } from '@/types/api'

interface Props {
  modelValue: PaymentChannel | null
  paymentChannels: PaymentChannel[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: PaymentChannel | null]
}>()

// Computed
const selectedMethod = computed(() => props.modelValue)

// Methods
const selectMethod = (channel: PaymentChannel) => {
  if (isSelected(channel)) {
    emit('update:modelValue', null)
  } else {
    emit('update:modelValue', channel)
  }
}

const isSelected = (channel: PaymentChannel) => {
  return selectedMethod.value?.id === channel.id
}

const formatDiscount = (disrate: string, discoin: number) => {
  if (disrate.includes('%')) {
    return disrate
  }
  return discoin > 0 ? `+${formatCurrency(discoin)}` : disrate
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
}
</script>
