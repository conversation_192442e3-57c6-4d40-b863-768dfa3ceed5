<template>
  <div
    :class="[
      'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200',
      statusClasses,
      animated && 'animate-pulse'
    ]"
  >
    <!-- Status Icon -->
    <component
      :is="statusIcon"
      :class="['w-3 h-3 mr-1.5', iconClasses]"
    />
    
    <!-- Status Text -->
    <span>{{ statusText }}</span>
    
    <!-- Loading Spinner for Processing States -->
    <div
      v-if="isProcessing"
      class="w-3 h-3 ml-1.5 border border-current border-t-transparent rounded-full animate-spin"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  Loader,
  ArrowUpRight,
  ArrowDownLeft,
  RefreshCw
} from 'lucide-vue-next'

interface Props {
  status: string
  type?: 'deposit' | 'withdraw' | 'transfer'
  animated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'deposit',
  animated: false
})

// 状态映射配置
const statusConfig = {
  // 成功状态
  success: {
    icon: CheckCircle,
    classes: 'bg-green-100 text-green-800 border border-green-200',
    iconClasses: 'text-green-600',
    text: 'Success'
  },
  completed: {
    icon: CheckCircle,
    classes: 'bg-green-100 text-green-800 border border-green-200',
    iconClasses: 'text-green-600',
    text: 'Completed'
  },
  
  // 失败状态
  failed: {
    icon: XCircle,
    classes: 'bg-red-100 text-red-800 border border-red-200',
    iconClasses: 'text-red-600',
    text: 'Failed'
  },
  rejected: {
    icon: XCircle,
    classes: 'bg-red-100 text-red-800 border border-red-200',
    iconClasses: 'text-red-600',
    text: 'Rejected'
  },
  cancelled: {
    icon: XCircle,
    classes: 'bg-gray-100 text-gray-800 border border-gray-200',
    iconClasses: 'text-gray-600',
    text: 'Cancelled'
  },
  
  // 处理中状态
  pending: {
    icon: Clock,
    classes: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
    iconClasses: 'text-yellow-600',
    text: 'Pending'
  },
  processing: {
    icon: Loader,
    classes: 'bg-blue-100 text-blue-800 border border-blue-200',
    iconClasses: 'text-blue-600',
    text: 'Processing'
  },
  reviewing: {
    icon: Clock,
    classes: 'bg-orange-100 text-orange-800 border border-orange-200',
    iconClasses: 'text-orange-600',
    text: 'Under Review'
  },
  
  // 警告状态
  warning: {
    icon: AlertTriangle,
    classes: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
    iconClasses: 'text-yellow-600',
    text: 'Warning'
  },
  
  // 退款状态
  refunded: {
    icon: RefreshCw,
    classes: 'bg-purple-100 text-purple-800 border border-purple-200',
    iconClasses: 'text-purple-600',
    text: 'Refunded'
  },
  
  // 默认状态
  default: {
    icon: Clock,
    classes: 'bg-gray-100 text-gray-800 border border-gray-200',
    iconClasses: 'text-gray-600',
    text: 'Unknown'
  }
}

// 根据交易类型调整图标
const getTypeIcon = (baseIcon: any) => {
  if (props.status === 'success' || props.status === 'completed') {
    switch (props.type) {
      case 'deposit':
        return ArrowDownLeft
      case 'withdraw':
        return ArrowUpRight
      default:
        return baseIcon
    }
  }
  return baseIcon
}

const currentConfig = computed(() => {
  const config = statusConfig[props.status as keyof typeof statusConfig] || statusConfig.default
  return {
    ...config,
    icon: getTypeIcon(config.icon)
  }
})

const statusClasses = computed(() => currentConfig.value.classes)
const iconClasses = computed(() => currentConfig.value.iconClasses)
const statusIcon = computed(() => currentConfig.value.icon)
const statusText = computed(() => currentConfig.value.text)

const isProcessing = computed(() => {
  return ['pending', 'processing', 'reviewing'].includes(props.status)
})
</script>
