<template>
  <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
    <div class="flex items-start justify-between">
      <!-- Transaction Info -->
      <div class="flex-1">
        <div class="flex items-center space-x-3 mb-2">
          <!-- Payment Method Icon -->
          <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <component
              :is="getPaymentIcon(transaction.title)"
              class="w-5 h-5 text-gray-600"
            />
          </div>
          
          <!-- Transaction Details -->
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h4 class="font-medium text-gray-900">{{ transaction.title }}</h4>
              <div class="text-right">
                <div class="font-semibold text-gray-900">
                  {{ formatCurrency(parseCurrency(transaction.coin)) }}
                </div>
                <div
                  :class="[
                    'text-xs font-medium',
                    getStatusColor(transaction.status)
                  ]"
                >
                  {{ transaction.status_str }}
                </div>
              </div>
            </div>
            
            <!-- Order ID and Time -->
            <div class="flex items-center justify-between mt-1">
              <button
                @click="copyOrderId"
                class="text-sm text-gray-500 hover:text-gray-700 transition-colors flex items-center"
              >
                <span class="font-mono">{{ formatOrderId(transaction.orderid) }}</span>
                <Copy class="w-3 h-3 ml-1" />
              </button>
              <span class="text-sm text-gray-500">{{ transaction.time }}</span>
            </div>
          </div>
        </div>
        
        <!-- Memo (if available) -->
        <div v-if="transaction.memo" class="mt-2 text-sm text-gray-600">
          {{ transaction.memo }}
        </div>
      </div>
    </div>
    
    <!-- Status Indicator -->
    <div class="mt-3 flex items-center justify-between">
      <div class="flex items-center">
        <component
          :is="getStatusIcon(transaction.status)"
          :class="[
            'w-4 h-4 mr-2',
            getStatusColor(transaction.status)
          ]"
        />
        <span
          :class="[
            'text-sm font-medium',
            getStatusColor(transaction.status)
          ]"
        >
          {{ transaction.status_str }}
        </span>
      </div>
      
      <!-- Action Button (for failed transactions) -->
      <div v-if="transaction.status === 2" class="text-right">
        <button class="text-sm text-blue-600 hover:text-blue-700 font-medium">
          Retry
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  Copy,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-vue-next'
import { formatCurrency, parseCurrency } from '@/utils/currency'
import type { TransactionItem } from '@/types/api'

interface Props {
  transaction: TransactionItem
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'copy-order': [orderId: string]
}>()

// Methods
const getPaymentIcon = (paymentMethod: string) => {
  const method = paymentMethod.toLowerCase()
  if (method.includes('alipay') || method.includes('ali')) {
    return Smartphone
  } else if (method.includes('upi') || method.includes('paytm') || method.includes('phonepe') || method.includes('gpay')) {
    return Smartphone
  } else if (method.includes('bank')) {
    return Building2
  } else {
    return CreditCard
  }
}

const getStatusIcon = (status: number) => {
  switch (status) {
    case 0: return Clock      // Pending
    case 1: return CheckCircle // Success
    case 2: return XCircle    // Failed
    case 3: return RefreshCw  // Refund
    default: return Clock
  }
}

const getStatusColor = (status: number) => {
  switch (status) {
    case 0: return 'text-yellow-600'  // Pending
    case 1: return 'text-green-600'   // Success
    case 2: return 'text-red-600'     // Failed
    case 3: return 'text-blue-600'    // Refund
    default: return 'text-gray-600'
  }
}

const formatOrderId = (orderId: string) => {
  // Show first 8 and last 4 characters
  if (orderId.length <= 12) {
    return orderId
  }
  return `${orderId.slice(0, 8)}...${orderId.slice(-4)}`
}

const copyOrderId = async () => {
  try {
    await navigator.clipboard.writeText(props.transaction.orderid)
    emit('copy-order', props.transaction.orderid)
  } catch (error) {
    console.error('Failed to copy order ID:', error)
  }
}
</script>
