<template>
  <div class="space-y-4">
    <!-- Grouped by Date (for deposits) -->
    <div v-if="groupedByDate && groupedTransactions">
      <div
        v-for="(dayTransactions, date) in groupedTransactions"
        :key="date"
        class="space-y-3"
      >
        <!-- Date Header -->
        <div class="flex items-center">
          <div class="flex-1 border-t border-gray-200"></div>
          <div class="px-4 py-2 bg-gray-100 rounded-full text-sm font-medium text-gray-600">
            {{ formatDate(date) }}
          </div>
          <div class="flex-1 border-t border-gray-200"></div>
        </div>
        
        <!-- Transactions for this date -->
        <div class="space-y-2">
          <TransactionItem
            v-for="transaction in dayTransactions"
            :key="transaction.id"
            :transaction="transaction"
            @copy-order="handleCopyOrder"
          />
        </div>
      </div>
    </div>
    
    <!-- Simple List (for other transaction types) -->
    <div v-else class="space-y-2">
      <TransactionItem
        v-for="transaction in transactions"
        :key="transaction.id"
        :transaction="transaction"
        @copy-order="handleCopyOrder"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TransactionItem as TransactionItemType } from '@/types/api'
import TransactionItem from './TransactionItem.vue'

interface Props {
  transactions: TransactionItemType[]
  groupedByDate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  groupedByDate: false
})

const emit = defineEmits<{
  'copy-order': [orderId: string]
}>()

// Computed
const groupedTransactions = computed(() => {
  if (!props.groupedByDate) return null
  
  const grouped: Record<string, TransactionItemType[]> = {}
  
  props.transactions.forEach(transaction => {
    // Extract date from transaction (assuming we have a date field or can derive it)
    const date = getTransactionDate(transaction)
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(transaction)
  })
  
  return grouped
})

// Methods
const getTransactionDate = (transaction: TransactionItemType): string => {
  // For now, use today's date as we don't have date info in the transaction
  // In real implementation, this would come from the transaction data
  const today = new Date()
  return today.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  })
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }
}

const handleCopyOrder = (orderId: string) => {
  emit('copy-order', orderId)
}
</script>
