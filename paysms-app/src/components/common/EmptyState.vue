<template>
  <div class="text-center py-12">
    <!-- Icon -->
    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <component
        :is="iconComponent"
        class="w-8 h-8 text-gray-400"
      />
    </div>
    
    <!-- Title -->
    <h3 class="text-lg font-medium text-gray-900 mb-2">
      {{ title }}
    </h3>
    
    <!-- Description -->
    <p class="text-gray-600 mb-6 max-w-sm mx-auto">
      {{ description }}
    </p>
    
    <!-- Action Button -->
    <div v-if="$slots.action">
      <slot name="action" />
    </div>
    <ActionButton
      v-else-if="actionText && actionHandler"
      :text="actionText"
      variant="primary"
      @click="actionHandler"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  CreditCard, 
  Download, 
  Dice6, 
  Gift, 
  FileText, 
  Search,
  AlertCircle,
  Inbox
} from 'lucide-vue-next'
import ActionButton from './ActionButton.vue'

interface Props {
  title: string
  description: string
  icon?: string
  actionText?: string
  actionHandler?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'inbox'
})

const emit = defineEmits<{
  action: []
}>()

const iconComponents = {
  'credit-card': CreditCard,
  download: Download,
  'dice-6': Dice6,
  gift: Gift,
  'file-text': FileText,
  search: Search,
  'alert-circle': AlertCircle,
  inbox: Inbox
}

const iconComponent = computed(() => {
  return iconComponents[props.icon as keyof typeof iconComponents] || Inbox
})
</script>
