<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-y-2 scale-95"
      enter-to-class="opacity-100 translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0 scale-100"
      leave-to-class="opacity-0 translate-y-2 scale-95"
    >
      <div
        v-if="visible"
        :class="[
          'fixed top-4 left-4 right-4 z-50 mx-auto max-w-sm',
          'rounded-xl shadow-lg border backdrop-blur-sm',
          'flex items-center p-4 space-x-3',
          variantClasses
        ]"
        role="alert"
      >
        <!-- Icon -->
        <div :class="['flex-shrink-0', iconColorClasses]">
          <component :is="iconComponent" class="w-5 h-5" />
        </div>

        <!-- Content -->
        <div class="flex-1 min-w-0">
          <p v-if="title" :class="['text-sm font-medium', titleColorClasses]">
            {{ title }}
          </p>
          <p :class="['text-sm', messageColorClasses]">
            {{ message }}
          </p>
        </div>

        <!-- Close Button -->
        <button
          v-if="closable"
          @click="close"
          :class="['flex-shrink-0 p-1 rounded-lg transition-colors', closeButtonClasses]"
        >
          <X class="w-4 h-4" />
        </button>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-vue-next'

interface Props {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  persistent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 4000,
  closable: true,
  persistent: false
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
let timeoutId: NodeJS.Timeout | null = null

const iconComponents = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info
}

const iconComponent = computed(() => iconComponents[props.type])

const variantClasses = computed(() => {
  const variants = {
    success: 'bg-green-50/90 border-green-200',
    error: 'bg-red-50/90 border-red-200',
    warning: 'bg-yellow-50/90 border-yellow-200',
    info: 'bg-blue-50/90 border-blue-200'
  }
  return variants[props.type]
})

const iconColorClasses = computed(() => {
  const colors = {
    success: 'text-green-600',
    error: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600'
  }
  return colors[props.type]
})

const titleColorClasses = computed(() => {
  const colors = {
    success: 'text-green-900',
    error: 'text-red-900',
    warning: 'text-yellow-900',
    info: 'text-blue-900'
  }
  return colors[props.type]
})

const messageColorClasses = computed(() => {
  const colors = {
    success: 'text-green-800',
    error: 'text-red-800',
    warning: 'text-yellow-800',
    info: 'text-blue-800'
  }
  return colors[props.type]
})

const closeButtonClasses = computed(() => {
  const colors = {
    success: 'text-green-600 hover:bg-green-100',
    error: 'text-red-600 hover:bg-red-100',
    warning: 'text-yellow-600 hover:bg-yellow-100',
    info: 'text-blue-600 hover:bg-blue-100'
  }
  return colors[props.type]
})

const show = () => {
  visible.value = true

  if (!props.persistent && props.duration > 0) {
    timeoutId = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const close = () => {
  visible.value = false
  if (timeoutId) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  emit('close')
}

onMounted(() => {
  show()
})

defineExpose({
  show,
  close
})
</script>
