<template>
  <div
    class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer"
    @click="handleClick"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- Icon -->
        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <component
            :is="iconComponent"
            class="w-5 h-5 text-blue-600"
          />
        </div>
        
        <!-- Content -->
        <div>
          <h3 class="font-medium text-gray-900">{{ title }}</h3>
          <p v-if="subtitle" class="text-sm text-gray-600 mt-0.5">
            {{ subtitle }}
          </p>
        </div>
      </div>
      
      <!-- Arrow -->
      <ChevronRight class="w-5 h-5 text-gray-400" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ChevronRight,
  History,
  CreditCard,
  Settings,
  User,
  HelpCircle,
  Bell,
  Shield
} from 'lucide-vue-next'

interface Props {
  title: string
  subtitle?: string
  icon: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: []
}>()

const iconComponents = {
  history: History,
  'credit-card': CreditCard,
  settings: Settings,
  user: User,
  help: HelpCircle,
  bell: Bell,
  shield: Shield
}

const iconComponent = computed(() => {
  return iconComponents[props.icon as keyof typeof iconComponents] || History
})

const handleClick = () => {
  emit('click')
}
</script>
