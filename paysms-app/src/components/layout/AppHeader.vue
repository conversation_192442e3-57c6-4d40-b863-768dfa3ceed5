<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="flex items-center justify-between px-4 py-3">
      <!-- Back Button (if needed) -->
      <button
        v-if="showBackButton"
        @click="goBack"
        class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <ChevronLeft class="w-5 h-5 text-gray-600" />
      </button>
      <div v-else class="w-9"></div>

      <!-- Title -->
      <h1 class="text-lg font-semibold text-gray-900 text-center flex-1">
        {{ title }}
      </h1>

      <!-- Right Action (Language Switcher/Menu) -->
      <div class="flex items-center space-x-2">
        <LanguageSwitcher variant="header" />
        <button
          v-if="showMenu"
          @click="toggleMenu"
          class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <Menu class="w-5 h-5 text-gray-600" />
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ChevronLeft, Menu } from 'lucide-vue-next'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'

interface Props {
  title: string
  showBackButton?: boolean
  showMenu?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBackButton: false,
  showMenu: true
})

const router = useRouter()

const goBack = () => {
  router.back()
}

const toggleMenu = () => {
  // TODO: 实现菜单切换逻辑
  console.log('Toggle menu')
}
</script>
