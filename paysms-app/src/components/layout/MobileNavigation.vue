<template>
  <div class="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 safe-area-pb">
    <!-- Navigation Items -->
    <div class="flex items-center justify-around px-2 py-2">
      <router-link
        v-for="item in navigationItems"
        :key="item.name"
        :to="item.path"
        :class="[
          'flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-200',
          'min-w-[60px] min-h-[60px] relative',
          isActive(item.path)
            ? 'bg-blue-50 text-blue-600 scale-105'
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 active:scale-95'
        ]"
        @click="handleNavClick(item)"
      >
        <!-- Icon with Badge -->
        <div class="relative">
          <component
            :is="getIcon(item.icon)"
            :class="[
              'w-6 h-6 transition-all duration-200',
              isActive(item.path) ? 'scale-110' : ''
            ]"
          />
          
          <!-- Badge for notifications -->
          <div
            v-if="item.badge && item.badge > 0"
            class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"
          >
            {{ item.badge > 99 ? '99+' : item.badge }}
          </div>
        </div>
        
        <!-- Label -->
        <span
          :class="[
            'text-xs font-medium mt-1 transition-all duration-200',
            isActive(item.path) ? 'scale-105' : ''
          ]"
        >
          {{ $t(item.label) }}
        </span>
        
        <!-- Active Indicator -->
        <div
          v-if="isActive(item.path)"
          class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"
        />
      </router-link>
    </div>
    
    <!-- Safe Area Padding -->
    <div class="h-safe-area-inset-bottom" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Home, 
  Plus, 
  ArrowDownToLine, 
  History, 
  CreditCard,
  User
} from 'lucide-vue-next'

interface NavigationItem {
  name: string
  path: string
  icon: string
  label: string
  badge?: number
}

const route = useRoute()

const navigationItems: NavigationItem[] = [
  {
    name: 'home',
    path: '/',
    icon: 'home',
    label: 'nav.home'
  },
  {
    name: 'deposit',
    path: '/pages/deposit/add/add',
    icon: 'plus',
    label: 'nav.deposit'
  },
  {
    name: 'withdraw',
    path: '/pages/withdraw/index',
    icon: 'arrow-down-to-line',
    label: 'nav.withdraw'
  },
  {
    name: 'transactions',
    path: '/pages/transaction/transaction',
    icon: 'history',
    label: 'nav.transactions'
  },
  {
    name: 'payments',
    path: '/pages/deposit/managepayment/managepayment',
    icon: 'credit-card',
    label: 'nav.payments'
  }
]

const iconComponents = {
  home: Home,
  plus: Plus,
  'arrow-down-to-line': ArrowDownToLine,
  history: History,
  'credit-card': CreditCard,
  user: User
}

const getIcon = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || Home
}

const isActive = (path: string) => {
  return route.path === path
}

const handleNavClick = async (item: NavigationItem) => {
  // 添加触觉反馈
  const { useHaptics } = await import('@/composables/useHaptics')
  const { selection } = useHaptics()
  selection()
}
</script>

<style scoped>
/* Safe area support */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.h-safe-area-inset-bottom {
  height: env(safe-area-inset-bottom);
}

/* Touch optimization */
@media (hover: none) and (pointer: coarse) {
  .router-link-active {
    /* 移动设备上的特殊样式 */
  }
}

/* 防止双击缩放 */
* {
  touch-action: manipulation;
}
</style>
