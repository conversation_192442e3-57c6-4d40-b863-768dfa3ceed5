# PaySMS 支付管理系统 - 详细设计方案

## 项目概述

基于目标网站 `http://paysms.haiwailaba.cyou/#/` 的逆向工程，重新创建完整的支付管理系统。该系统主要用于余额管理、充值、提现、交易记录等金融操作。

### 目标网站分析结果

**核心功能模块：**

- 余额管理：总余额、未使用金额、奖金、现金奖金显示
- 充值系统：支持多种支付方式（AliPay、UPI 等）
- 提现系统：银行账户管理、提现操作
- 交易记录：分类显示存款、提现、投注、奖金记录
- 支付管理：银行账户和电子钱包管理
- 账户验证：银行卡添加和验证流程

**技术特征：**

- 移动端优先的响应式设计
- Vue.js 单页应用架构
- 印度卢比（₹）货币体系
- 实时数据更新和 API 集成
- 文件上传和表单验证功能

## 技术栈配置

### 核心技术栈

```json
{
  "packageManager": "bun",
  "frontend": "Vue 3 (Composition API)",
  "buildTool": "Vite",
  "uiLibrary": "shadcn-vue",
  "cssFramework": "Tailwind CSS",
  "router": "Vue Router 4",
  "stateManagement": "Pinia",
  "i18n": "Vue I18n",
  "httpClient": "Axios",
  "typeSystem": "TypeScript"
}
```

### 依赖包清单

```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "vue-i18n": "^9.8.0",
    "axios": "^1.6.0",
    "@vueuse/core": "^10.7.0",
    "radix-vue": "^1.4.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.2.0",
    "lucide-vue-next": "^0.307.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.0",
    "typescript": "^5.3.0",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "@types/node": "^20.10.0"
  }
}
```

## 页面结构和路由设计

### 路由配置

```typescript
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: 'My Balance' }
  },
  {
    path: '/pages/deposit/add/add',
    name: 'Deposit',
    component: () => import('@/views/Deposit/Add.vue'),
    meta: { title: 'Deposit' }
  },
  {
    path: '/pages/withdraw/index',
    name: 'Withdraw',
    component: () => import('@/views/Withdraw/Index.vue'),
    meta: { title: 'WITHDRAW' }
  },
  {
    path: '/pages/transaction/transaction',
    name: 'Transactions',
    component: () => import('@/views/Transaction/Transaction.vue'),
    meta: { title: 'My Transactions' }
  },
  {
    path: '/pages/deposit/managepayment/managepayment',
    name: 'ManagePayments',
    component: () => import('@/views/Deposit/ManagePayment.vue'),
    meta: { title: 'Manage Payments' }
  },
  {
    path: '/pages/deposit/addcreditcard/addcreditcard',
    name: 'AddBankAccount',
    component: () => import('@/views/Deposit/AddCreditCard.vue'),
    meta: { title: 'Add New Bank Account' }
  }
]
```

### 页面组件结构

```
src/
├── views/
│   ├── Home.vue                    # 首页 - 余额概览
│   ├── Deposit/
│   │   ├── Add.vue                 # 充值页面
│   │   ├── ManagePayment.vue       # 支付管理
│   │   └── AddCreditCard.vue       # 添加银行卡
│   ├── Withdraw/
│   │   └── Index.vue               # 提现页面
│   └── Transaction/
│       └── Transaction.vue         # 交易记录
├── components/
│   ├── ui/                         # shadcn-vue 组件
│   ├── layout/
│   │   ├── AppHeader.vue           # 页面头部
│   │   ├── AppNavigation.vue       # 底部导航
│   │   └── AppLayout.vue           # 布局容器
│   ├── balance/
│   │   ├── BalanceCard.vue         # 余额卡片
│   │   ├── BalanceItem.vue         # 余额项目
│   │   └── QuickActions.vue        # 快捷操作
│   ├── deposit/
│   │   ├── AmountInput.vue         # 金额输入
│   │   ├── PaymentMethod.vue       # 支付方式选择
│   │   └── PaymentChannel.vue      # 支付渠道选择
│   ├── withdraw/
│   │   ├── BankAccountCard.vue     # 银行账户卡片
│   │   └── WithdrawForm.vue        # 提现表单
│   ├── transaction/
│   │   ├── TransactionTabs.vue     # 交易标签
│   │   ├── TransactionList.vue     # 交易列表
│   │   └── TransactionItem.vue     # 交易项目
│   └── common/
│       ├── CurrencyDisplay.vue     # 货币显示
│       ├── StatusBadge.vue         # 状态徽章
│       ├── LoadingSpinner.vue      # 加载动画
│       └── ErrorMessage.vue        # 错误消息
```

## 状态管理设计

### Pinia Store 结构

```typescript
// stores/user.ts - 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    profile: null as UserProfile | null,
    isAuthenticated: false,
    balance: {
      total: 0,
      unutilized: 0,
      winnings: 0,
      cashBonus: 0
    } as BalanceInfo
  }),
  actions: {
    async fetchBalance(),
    async updateProfile(),
    logout()
  }
})

// stores/payment.ts - 支付状态管理
export const usePaymentStore = defineStore('payment', {
  state: () => ({
    bankAccounts: [] as BankAccount[],
    wallets: [] as Wallet[],
    paymentMethods: [] as PaymentMethod[],
    paymentChannels: [] as PaymentChannel[]
  }),
  actions: {
    async fetchBankAccounts(),
    async addBankAccount(),
    async fetchWallets(),
    async linkWallet()
  }
})

// stores/transaction.ts - 交易状态管理
export const useTransactionStore = defineStore('transaction', {
  state: () => ({
    deposits: [] as Transaction[],
    withdrawals: [] as Transaction[],
    bets: [] as Transaction[],
    bonuses: [] as Transaction[],
    loading: false,
    error: null as string | null
  }),
  actions: {
    async fetchTransactions(),
    async createDeposit(),
    async createWithdrawal()
  }
})
```

## API 设计和数据模型

### 实际 API 端点分析

基于对原网站的深入分析，发现以下真实的 API 端点：

```typescript
// api/endpoints.ts
export const API_ENDPOINTS = {
  // 基础服务域名
  BASE_URL: 'http://service.haiwailaba.cyou',

  // 用户相关
  USER_BALANCE: '/user/balance', // 获取用户余额和支付渠道
  USER_BALANCE_WITH_PARAMS: '/user/balance?cat=2&coin=200&paypop=0', // 充值页面余额
  USER_BANKS: '/user/banks', // 获取用户银行账户
  USER_BANKS_SPLIT: '/user/banks?split=1', // 获取银行账户和UPI钱包
  USER_HISTORY: '/user/history', // 获取交易历史

  // 交易相关
  DRAW_INDEX: '/draw/index', // 提现相关信息
  DRAW_INDEX_WITH_PARAMS: '/draw/index?drawpop=0', // 提现页面信息

  // 分页参数
  HISTORY_PAGINATION: '?num=1&size=10', // 交易历史分页

  // 文件上传 (推测)
  UPLOAD_FILE: '/upload'
}
```

### 基于真实 API 的 TypeScript 数据模型

```typescript
// types/api.ts - 通用API响应格式
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// types/user.ts - 用户相关类型
export interface UserInfo {
  uid: number
  coin: number // 总余额
  dcoin: number // 可提现余额
  ecoin: number // 已添加金额(未使用)
  bonus: number // 现金奖金
  totalcoin: number // 总余额
  kyc: number // KYC状态 (0=未验证, 1=已验证)
  svip: number // VIP状态
  ispayer: number // 支付者状态
  nodislabelid: number // 标签ID
}

export interface BalanceResponse {
  user: UserInfo
  channels: PaymentChannel[]
  memo: string // 提示信息 (HTML格式)
  popmsg: string // 弹窗消息
  customer: any[] // 客服信息
  url: string // 客服URL
}

// types/payment.ts - 支付相关类型
export interface PaymentChannel {
  id: number
  title: string // 支付方式名称 (如 "AliPay", "upi")
  icon: string // 图标URL
  subtype: string // 子类型 (如 "onlinepay")
  mincoin: number // 最小金额
  maxcoin: number // 最大金额
  disrate: string // 折扣率显示 (如 "+500", "+3.00%")
  discoin: number // 折扣金额
  type: number // 类型
  pages: PaymentPage[] // 支付页面配置
}

export interface PaymentPage {
  id: number
  title: string // 页面标题 (如 "test1009", "Paytm APP")
  type: number // 页面类型
  banktype: number // 银行类型
  mincoin: number // 最小金额
  maxcoin: number // 最大金额
  disrate: string // 折扣率显示
  discoin: number // 折扣金额
  rate: number // 费率 (小数形式，如 0.03 表示 3%)
}

export interface BankAccount {
  id: number
  accountNumber: string
  ifscCode: string
  accountHolderName: string
  bankName: string
  isVerified: boolean
}

export interface UpiWallet {
  id: number
  name: string // 钱包名称 (如 "Paytm", "Phonepe")
  checked: boolean // 是否已验证
  card: string // 状态文本 (如 "Link")
  icon: string // 图标路径
  cat: number // 分类ID
}

// types/transaction.ts - 交易相关类型
export interface TransactionItem {
  id: number
  orderid: string // 订单ID
  coin: string // 金额 (字符串格式，如 "300.00")
  status_str: string // 状态文本 (如 "In-Process", "Success")
  status: number // 状态码 (0=处理中, 1=成功, 2=失败, 3=退款)
  time: string // 时间 (如 "12:48 pm")
  title: string // 支付方式 (如 "AliPay", "upi")
  memo: string // 备注
}

export interface TransactionHistory {
  deplist: Record<string, TransactionItem[]> // 存款记录 (按日期分组)
  drawlist: TransactionItem[] // 提现记录
  betlist: TransactionItem[] // 投注记录
  bonuslist: TransactionItem[] // 奖金记录
  banklist: TransactionItem[] // 银行记录
  show: number // 显示标志
  url: string // 客服URL
}

export interface BanksResponse {
  banks: BankAccount[] // 银行账户列表
  upis: Record<string, UpiWallet> // UPI钱包 (按ID索引)
  kyc: number // KYC状态
  url: string // 客服URL
}

export interface DrawInfo {
  uid: number // 用户ID
  dcoin: number // 可提现金额
  limit: number // 提现限制
  mincoin: number // 最小提现金额
  maxcoin: number // 最大提现金额
  memo: string // 提示信息 (HTML格式)
  popmsg: string // 弹窗消息
  customer: any[] // 客服信息
  url: string // 客服URL
}
```

### API 响应示例

基于实际分析，以下是主要 API 的真实响应格式：

```typescript
// GET /user/balance - 用户余额和支付渠道
const balanceResponse: ApiResponse<BalanceResponse> = {
  code: 0,
  msg: 'succ',
  data: {
    user: {
      uid: 10002,
      coin: 10,
      dcoin: 0,
      ecoin: 10,
      bonus: 0,
      totalcoin: 10,
      kyc: 1,
      svip: 0,
      ispayer: 0,
      nodislabelid: 0
    },
    channels: [
      {
        id: 10,
        title: 'AliPay',
        icon: 'https://img.yonogames.com//upload/********/9e2244e45601065efb0a9247cf29f689.png',
        subtype: 'onlinepay',
        mincoin: 100,
        maxcoin: 1000000,
        disrate: '+500',
        discoin: 500,
        type: 0,
        pages: [
          {
            id: 10,
            title: 'test1009',
            type: 1,
            banktype: 0,
            mincoin: 100,
            maxcoin: 1000000,
            disrate: '+500',
            discoin: 500,
            rate: 0
          }
        ]
      }
    ],
    memo: 'Tips: aaaa<br/>\\nbbbbb<br/>\\nccccc',
    popmsg: '',
    customer: [],
    url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
  }
}

// GET /user/history?num=1&size=10 - 交易历史
const historyResponse: ApiResponse<TransactionHistory> = {
  code: 0,
  msg: 'succ',
  data: {
    deplist: {
      '07/30/2025': [
        {
          id: 5,
          orderid: '202507301248350198519953',
          coin: '300.00',
          status_str: 'In-Process',
          status: 0,
          time: '12:48 pm',
          title: 'AliPay',
          memo: ''
        }
      ]
    },
    drawlist: [],
    betlist: [],
    bonuslist: [],
    banklist: [],
    show: 1,
    url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
  }
}

// GET /user/banks?split=1 - 银行账户和UPI钱包
const banksResponse: ApiResponse<BanksResponse> = {
  code: 0,
  msg: 'succ',
  data: {
    banks: [],
    upis: {
      '1': {
        id: 1,
        name: 'Paytm',
        checked: true,
        card: 'Link',
        icon: '/static/img/paytm.png',
        cat: 1
      }
    },
    kyc: 1,
    url: 'https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4'
  }
}
```

## 国际化方案

### 支持语言

- 中文 (简体) - zh-CN (默认)
- 英文 (English) - en-US
- 印地语 (हिन्दी) - hi-IN
- 葡萄牙语 (Português - 巴西) - pt-BR

### i18n 配置结构

```typescript
// locales/zh-CN.ts
export default {
  common: {
    balance: '余额',
    deposit: '充值',
    withdraw: '提现',
    transactions: '交易记录',
    amount: '金额',
    submit: '提交',
    cancel: '取消',
    loading: '加载中...',
    error: '错误',
    success: '成功'
  },
  home: {
    title: '我的余额',
    totalBalance: '总余额',
    amountAdded: '已添加金额(未使用)',
    winnings: '奖金',
    cashBonus: '现金奖金',
    addCash: '充值',
    withdrawInstantly: '立即提现',
    verifyNow: '立即验证',
    myTransactions: '我的交易',
    managePayments: '管理支付方式'
  },
  deposit: {
    title: '充值',
    currentBalance: '当前余额',
    paymentMethod: '支付方式',
    paymentChannel: '支付渠道',
    depositNow: '立即充值',
    minMaxAmount: '充值最小金额：₹{min}，最大金额：₹{max}',
    tips: '提示'
  },
  withdraw: {
    title: '提现',
    withdrawableBalance: '可提现余额',
    addNewBankAccount: '添加新银行账户',
    withdrawNow: '立即提现',
    minMaxAmount: '提现最小金额：₹{min}，最大金额：₹{max}'
  }
}
```

## UI/UX 设计规范

### 颜色方案

```css
:root {
  /* 主色调 */
  --primary: #3b82f6;
  --primary-foreground: #ffffff;

  /* 背景色 */
  --background: #ffffff;
  --card-background: #f8fafc;

  /* 文字颜色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  /* 状态颜色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* 边框和分割线 */
  --border: #e2e8f0;
  --divider: #f1f5f9;
}
```

### 组件设计规范

```typescript
// 按钮样式变体
const buttonVariants = {
  primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
  secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  outline: 'border border-input bg-background hover:bg-accent',
  ghost: 'hover:bg-accent hover:text-accent-foreground'
}

// 卡片样式
const cardStyles = {
  base: 'rounded-lg border bg-card text-card-foreground shadow-sm',
  balance: 'p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white',
  transaction: 'p-4 border-l-4 border-l-blue-500'
}

// 间距系统
const spacing = {
  xs: '0.25rem', // 4px
  sm: '0.5rem', // 8px
  md: '1rem', // 16px
  lg: '1.5rem', // 24px
  xl: '2rem', // 32px
  '2xl': '3rem' // 48px
}
```

### 响应式断点

```css
/* 移动端优先设计 */
@media (min-width: 640px) {
  /* sm */
}
@media (min-width: 768px) {
  /* md */
}
@media (min-width: 1024px) {
  /* lg */
}
@media (min-width: 1280px) {
  /* xl */
}
```

## 开发计划和里程碑

### 第一阶段：项目初始化 (1-2 天)

- [x] 网站分析和设计方案制定
- [ ] 创建 Vue 3 + Vite 项目
- [ ] 配置 TypeScript、Tailwind CSS、shadcn-vue
- [ ] 设置路由、状态管理、国际化
- [ ] 创建基础项目结构

### 第二阶段：核心页面开发 (3-5 天)

- [ ] 首页 - 余额概览页面
- [ ] 充值页面 - 完整充值流程
- [ ] 提现页面 - 提现功能实现
- [ ] 基础组件库开发

### 第三阶段：功能完善 (2-3 天)

- [ ] 交易记录页面
- [ ] 支付管理页面
- [ ] 银行卡添加页面
- [ ] API 集成和数据处理

### 第四阶段：优化和测试 (1-2 天)

- [ ] 响应式设计优化
- [ ] 多语言翻译完善
- [ ] 性能优化
- [ ] 错误处理和用户体验改进
- [ ] 全面测试

### 第五阶段：部署和文档 (1 天)

- [ ] 构建配置优化
- [ ] 部署准备
- [ ] 使用文档编写
- [ ] 项目交付

## 技术实现细节

### 文件上传处理

```typescript
// composables/useFileUpload.ts
export function useFileUpload() {
  const uploadFile = async (file: File): Promise<string> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await axios.post('/api/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })

    return response.data.url
  }

  return { uploadFile }
}
```

### 表单验证

```typescript
// composables/useFormValidation.ts
export function useFormValidation() {
  const validateBankAccount = (data: BankAccountForm) => {
    const errors: Record<string, string> = {}

    if (!data.accountNumber) {
      errors.accountNumber = 'Account number is required'
    } else if (!/^\d{9,18}$/.test(data.accountNumber)) {
      errors.accountNumber = 'Invalid account number format'
    }

    if (!data.ifscCode) {
      errors.ifscCode = 'IFSC code is required'
    } else if (!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(data.ifscCode)) {
      errors.ifscCode = 'Invalid IFSC code format'
    }

    return { isValid: Object.keys(errors).length === 0, errors }
  }

  return { validateBankAccount }
}
```

### 货币格式化

```typescript
// utils/currency.ts
export function formatCurrency(amount: number, currency = '₹'): string {
  return `${currency}${amount.toLocaleString('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

export function parseCurrency(value: string): number {
  return parseFloat(value.replace(/[^\d.-]/g, '')) || 0
}
```

## 安全考虑

### 数据验证

- 前端表单验证 + 后端 API 验证
- 金额输入限制和格式验证
- 文件上传类型和大小限制
- XSS 防护和输入清理

### 敏感信息处理

- 银行账户信息加密存储
- API 请求认证和授权
- 敏感操作二次确认
- 错误信息不暴露敏感数据

## 性能优化

### 代码分割

```typescript
// 路由级别的代码分割
const routes = [
  {
    path: '/',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/pages/deposit/add/add',
    component: () => import('@/views/Deposit/Add.vue')
  }
]
```

### 图片优化

- 使用 WebP 格式
- 懒加载实现
- 响应式图片
- 图标使用 SVG

### 缓存策略

- API 响应缓存
- 静态资源缓存
- 用户状态持久化
- 离线数据支持

---

**总结：** 本设计方案基于对目标网站的深入分析，采用现代化的技术栈和最佳实践，确保项目的可维护性、可扩展性和用户体验。通过分阶段的开发计划，可以高效地完成项目交付。
